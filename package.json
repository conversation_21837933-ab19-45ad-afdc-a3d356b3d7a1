{"name": "formcraft-saas", "version": "1.0.0", "description": "A comprehensive Jotform-like SaaS platform with AI-powered form building", "private": true, "type": "module", "scripts": {"dev": "vite", "build": "tsc && vite build", "preview": "vite preview", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "lint:fix": "eslint . --ext ts,tsx --fix", "type-check": "tsc --noEmit", "test": "vitest", "test:ui": "vitest --ui", "test:coverage": "vitest --coverage", "supabase:start": "supabase start", "supabase:stop": "supabase stop", "supabase:reset": "supabase db reset", "supabase:generate-types": "supabase gen types typescript --local > src/types/supabase.ts", "deploy": "npm run build && vercel deploy --prod"}, "dependencies": {"react": "^18.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.20.1", "@supabase/supabase-js": "^2.38.4", "@supabase/auth-helpers-react": "^0.4.2", "@supabase/auth-ui-react": "^0.4.6", "@supabase/auth-ui-shared": "^0.1.8", "@tanstack/react-query": "^5.8.4", "@tanstack/react-query-devtools": "^5.8.4", "zustand": "^4.4.7", "react-hook-form": "^7.48.2", "@hookform/resolvers": "^3.3.2", "zod": "^3.22.4", "react-beautiful-dnd": "^13.1.1", "@dnd-kit/core": "^6.1.0", "@dnd-kit/sortable": "^8.0.0", "@dnd-kit/utilities": "^3.2.2", "framer-motion": "^10.16.5", "lucide-react": "^0.294.0", "react-hot-toast": "^2.4.1", "recharts": "^2.8.0", "date-fns": "^2.30.0", "react-datepicker": "^4.24.0", "react-signature-canvas": "^1.0.6", "qrcode": "^1.5.3", "html2canvas": "^1.4.1", "jspdf": "^2.5.1", "file-saver": "^2.0.5", "papaparse": "^5.4.1", "stripe": "^14.7.0", "@stripe/stripe-js": "^2.2.0", "@stripe/react-stripe-js": "^2.4.0", "react-confetti": "^6.1.0", "react-markdown": "^9.0.1", "remark-gfm": "^4.0.0", "react-syntax-highlighter": "^15.5.0", "react-color": "^2.19.3", "react-select": "^5.8.0", "react-dropzone": "^14.2.3", "react-virtualized-auto-sizer": "^1.0.20", "react-window": "^1.8.8", "react-intersection-observer": "^9.5.3", "workbox-precaching": "^7.0.0", "workbox-routing": "^7.0.0", "workbox-strategies": "^7.0.0"}, "devDependencies": {"@types/react": "^18.2.37", "@types/react-dom": "^18.2.15", "@types/react-beautiful-dnd": "^13.1.8", "@types/qrcode": "^1.5.5", "@types/file-saver": "^2.0.7", "@types/papaparse": "^5.3.14", "@types/react-color": "^3.0.9", "@types/react-syntax-highlighter": "^15.5.11", "@types/react-window": "^1.8.8", "@typescript-eslint/eslint-plugin": "^6.10.0", "@typescript-eslint/parser": "^6.10.0", "@vitejs/plugin-react-swc": "^3.5.0", "eslint": "^8.53.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.4", "typescript": "^5.2.2", "vite": "^5.0.0", "vite-plugin-pwa": "^0.17.4", "vitest": "^1.0.0", "@vitest/ui": "^1.0.0", "@vitest/coverage-v8": "^1.0.0", "@testing-library/react": "^14.1.2", "@testing-library/jest-dom": "^6.1.5", "@testing-library/user-event": "^14.5.1", "jsdom": "^23.0.1", "supabase": "^1.123.4", "tailwindcss": "^3.3.6", "autoprefixer": "^10.4.16", "postcss": "^8.4.32", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10", "class-variance-authority": "^0.7.0", "clsx": "^2.0.0", "tailwind-merge": "^2.0.0"}, "engines": {"node": ">=18.0.0", "npm": ">=9.0.0"}}