-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- <PERSON>reate custom types
CREATE TYPE user_role AS ENUM ('owner', 'admin', 'editor', 'viewer');
CREATE TYPE subscription_status AS ENUM ('active', 'canceled', 'past_due', 'unpaid', 'trialing');
CREATE TYPE payment_status AS ENUM ('pending', 'completed', 'failed', 'refunded');
CREATE TYPE form_status AS ENUM ('draft', 'published', 'archived');
CREATE TYPE integration_status AS ENUM ('connected', 'disconnected', 'error');
CREATE TYPE workflow_status AS ENUM ('active', 'inactive', 'error');

-- Tenants table (Organizations/Workspaces)
CREATE TABLE tenants (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    name VARCHAR(255) NOT NULL,
    slug VARCHAR(100) UNIQUE NOT NULL,
    domain VARCHAR(255),
    logo_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Team members table (User-Tenant relationship)
CREATE TABLE team_members (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role user_role NOT NULL DEFAULT 'viewer',
    invited_by UUID REFERENCES auth.users(id),
    invited_at TIMESTAMP WITH TIME ZONE,
    joined_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, user_id)
);

-- User profiles table
CREATE TABLE user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email VARCHAR(255) NOT NULL,
    full_name VARCHAR(255),
    avatar_url TEXT,
    timezone VARCHAR(50) DEFAULT 'UTC',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Form templates table
CREATE TABLE form_templates (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    title VARCHAR(255) NOT NULL,
    description TEXT,
    category VARCHAR(100),
    schema JSONB NOT NULL,
    preview_image_url TEXT,
    is_public BOOLEAN DEFAULT true,
    created_by UUID REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Forms table
CREATE TABLE forms (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    title VARCHAR(255) NOT NULL,
    description TEXT,
    schema JSONB NOT NULL DEFAULT '{"fields": []}',
    settings JSONB DEFAULT '{}',
    status form_status DEFAULT 'draft',
    compliance_mode VARCHAR(50),
    custom_css TEXT,
    thank_you_message TEXT,
    redirect_url TEXT,
    allow_multiple_submissions BOOLEAN DEFAULT true,
    require_login BOOLEAN DEFAULT false,
    collect_ip_address BOOLEAN DEFAULT true,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Form submissions table
CREATE TABLE submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    data JSONB NOT NULL,
    ip_address INET,
    user_agent TEXT,
    referrer TEXT,
    user_id UUID REFERENCES auth.users(id),
    payment_status payment_status,
    payment_amount DECIMAL(10,2),
    payment_currency VARCHAR(3),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Conditional logic rules table
CREATE TABLE conditional_logic (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    field_id VARCHAR(255) NOT NULL,
    conditions JSONB NOT NULL,
    actions JSONB NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Partial submissions for save & continue later
CREATE TABLE partial_submissions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    token VARCHAR(255) UNIQUE NOT NULL,
    data JSONB NOT NULL,
    expires_at TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Form payments table
CREATE TABLE form_payments (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    submission_id UUID REFERENCES submissions(id) ON DELETE SET NULL,
    provider VARCHAR(50) NOT NULL, -- 'stripe', 'paypal'
    provider_payment_id VARCHAR(255),
    amount DECIMAL(10,2) NOT NULL,
    currency VARCHAR(3) NOT NULL,
    status payment_status DEFAULT 'pending',
    metadata JSONB DEFAULT '{}',
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Form integrations table
CREATE TABLE form_integrations (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    service VARCHAR(100) NOT NULL, -- 'google_sheets', 'slack', 'notion', 'airtable'
    config JSONB NOT NULL,
    status integration_status DEFAULT 'disconnected',
    last_sync_at TIMESTAMP WITH TIME ZONE,
    error_message TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Form workflows table
CREATE TABLE form_workflows (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    form_id UUID NOT NULL REFERENCES forms(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    description TEXT,
    workflow JSONB NOT NULL,
    status workflow_status DEFAULT 'inactive',
    trigger_conditions JSONB DEFAULT '{}',
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Tenant branding table
CREATE TABLE tenant_branding (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE UNIQUE,
    logo_url TEXT,
    colors JSONB DEFAULT '{}',
    typography JSONB DEFAULT '{}',
    custom_css TEXT,
    favicon_url TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Custom domains table
CREATE TABLE custom_domains (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    domain VARCHAR(255) NOT NULL UNIQUE,
    status VARCHAR(50) DEFAULT 'pending', -- 'pending', 'verified', 'failed'
    ssl_status VARCHAR(50) DEFAULT 'pending',
    verification_token VARCHAR(255),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Subscriptions table
CREATE TABLE subscriptions (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE UNIQUE,
    stripe_customer_id VARCHAR(255),
    stripe_subscription_id VARCHAR(255),
    plan VARCHAR(50) NOT NULL DEFAULT 'free', -- 'free', 'pro', 'enterprise'
    status subscription_status DEFAULT 'active',
    current_period_start TIMESTAMP WITH TIME ZONE,
    current_period_end TIMESTAMP WITH TIME ZONE,
    cancel_at_period_end BOOLEAN DEFAULT false,
    trial_end TIMESTAMP WITH TIME ZONE,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE usage_tracking (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    metric VARCHAR(100) NOT NULL, -- 'forms_created', 'submissions_received', 'storage_used'
    value INTEGER NOT NULL DEFAULT 0,
    period_start TIMESTAMP WITH TIME ZONE NOT NULL,
    period_end TIMESTAMP WITH TIME ZONE NOT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    UNIQUE(tenant_id, metric, period_start)
);

-- Audit logs table
CREATE TABLE audit_logs (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    user_id UUID REFERENCES auth.users(id) ON DELETE SET NULL,
    action VARCHAR(100) NOT NULL,
    resource_type VARCHAR(100),
    resource_id UUID,
    metadata JSONB DEFAULT '{}',
    ip_address INET,
    user_agent TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- API keys table
CREATE TABLE api_keys (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    name VARCHAR(255) NOT NULL,
    key_hash VARCHAR(255) NOT NULL UNIQUE,
    permissions JSONB DEFAULT '[]',
    last_used_at TIMESTAMP WITH TIME ZONE,
    expires_at TIMESTAMP WITH TIME ZONE,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhooks table
CREATE TABLE webhooks (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    tenant_id UUID NOT NULL REFERENCES tenants(id) ON DELETE CASCADE,
    form_id UUID REFERENCES forms(id) ON DELETE CASCADE,
    url TEXT NOT NULL,
    events TEXT[] NOT NULL,
    secret VARCHAR(255),
    is_active BOOLEAN DEFAULT true,
    retry_count INTEGER DEFAULT 3,
    created_by UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Webhook deliveries table
CREATE TABLE webhook_deliveries (
    id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
    webhook_id UUID NOT NULL REFERENCES webhooks(id) ON DELETE CASCADE,
    event_type VARCHAR(100) NOT NULL,
    payload JSONB NOT NULL,
    response_status INTEGER,
    response_body TEXT,
    delivered_at TIMESTAMP WITH TIME ZONE,
    attempts INTEGER DEFAULT 0,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Create indexes for better performance
CREATE INDEX idx_team_members_tenant_id ON team_members(tenant_id);
CREATE INDEX idx_team_members_user_id ON team_members(user_id);
CREATE INDEX idx_forms_tenant_id ON forms(tenant_id);
CREATE INDEX idx_forms_status ON forms(status);
CREATE INDEX idx_submissions_form_id ON submissions(form_id);
CREATE INDEX idx_submissions_created_at ON submissions(created_at);
CREATE INDEX idx_form_payments_form_id ON form_payments(form_id);
CREATE INDEX idx_form_integrations_form_id ON form_integrations(form_id);
CREATE INDEX idx_audit_logs_tenant_id ON audit_logs(tenant_id);
CREATE INDEX idx_audit_logs_created_at ON audit_logs(created_at);
CREATE INDEX idx_usage_tracking_tenant_id ON usage_tracking(tenant_id);
CREATE INDEX idx_webhook_deliveries_webhook_id ON webhook_deliveries(webhook_id);

-- Enable Row Level Security
ALTER TABLE tenants ENABLE ROW LEVEL SECURITY;
ALTER TABLE team_members ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE forms ENABLE ROW LEVEL SECURITY;
ALTER TABLE submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE conditional_logic ENABLE ROW LEVEL SECURITY;
ALTER TABLE partial_submissions ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_payments ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE form_workflows ENABLE ROW LEVEL SECURITY;
ALTER TABLE tenant_branding ENABLE ROW LEVEL SECURITY;
ALTER TABLE custom_domains ENABLE ROW LEVEL SECURITY;
ALTER TABLE subscriptions ENABLE ROW LEVEL SECURITY;
ALTER TABLE usage_tracking ENABLE ROW LEVEL SECURITY;
ALTER TABLE audit_logs ENABLE ROW LEVEL SECURITY;
ALTER TABLE api_keys ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhooks ENABLE ROW LEVEL SECURITY;
ALTER TABLE webhook_deliveries ENABLE ROW LEVEL SECURITY;

-- Helper function to get user's tenant IDs
CREATE OR REPLACE FUNCTION get_user_tenant_ids(user_uuid UUID)
RETURNS UUID[] AS $$
BEGIN
    RETURN ARRAY(
        SELECT tenant_id
        FROM team_members
        WHERE user_id = user_uuid
    );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- RLS Policies for tenants
CREATE POLICY "Users can view their tenants" ON tenants
    FOR SELECT USING (id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Owners can update their tenants" ON tenants
    FOR UPDATE USING (
        id IN (
            SELECT tenant_id FROM team_members
            WHERE user_id = auth.uid() AND role = 'owner'
        )
    );

-- RLS Policies for team_members
CREATE POLICY "Users can view team members of their tenants" ON team_members
    FOR SELECT USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Owners and admins can manage team members" ON team_members
    FOR ALL USING (
        tenant_id IN (
            SELECT tenant_id FROM team_members
            WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
        )
    );

-- RLS Policies for user_profiles
CREATE POLICY "Users can view and update their own profile" ON user_profiles
    FOR ALL USING (id = auth.uid());

-- RLS Policies for forms
CREATE POLICY "Users can view forms of their tenants" ON forms
    FOR SELECT USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Users can create forms in their tenants" ON forms
    FOR INSERT WITH CHECK (
        tenant_id = ANY(get_user_tenant_ids(auth.uid())) AND
        created_by = auth.uid()
    );

CREATE POLICY "Users can update forms they created or have edit access" ON forms
    FOR UPDATE USING (
        tenant_id = ANY(get_user_tenant_ids(auth.uid())) AND
        (created_by = auth.uid() OR
         EXISTS(SELECT 1 FROM team_members WHERE user_id = auth.uid() AND tenant_id = forms.tenant_id AND role IN ('owner', 'admin', 'editor')))
    );

-- RLS Policies for submissions
CREATE POLICY "Users can view submissions of their tenant forms" ON submissions
    FOR SELECT USING (
        form_id IN (
            SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))
        )
    );

CREATE POLICY "Anyone can create submissions for published forms" ON submissions
    FOR INSERT WITH CHECK (
        form_id IN (SELECT id FROM forms WHERE status = 'published')
    );

-- RLS Policies for other tables (simplified for brevity)
CREATE POLICY "Tenant isolation" ON conditional_logic
    FOR ALL USING (form_id IN (SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

CREATE POLICY "Tenant isolation" ON partial_submissions
    FOR ALL USING (form_id IN (SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

CREATE POLICY "Tenant isolation" ON form_payments
    FOR ALL USING (form_id IN (SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

CREATE POLICY "Tenant isolation" ON form_integrations
    FOR ALL USING (form_id IN (SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

CREATE POLICY "Tenant isolation" ON form_workflows
    FOR ALL USING (form_id IN (SELECT id FROM forms WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

CREATE POLICY "Tenant isolation" ON tenant_branding
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON custom_domains
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON subscriptions
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON usage_tracking
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON audit_logs
    FOR SELECT USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON api_keys
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON webhooks
    FOR ALL USING (tenant_id = ANY(get_user_tenant_ids(auth.uid())));

CREATE POLICY "Tenant isolation" ON webhook_deliveries
    FOR SELECT USING (webhook_id IN (SELECT id FROM webhooks WHERE tenant_id = ANY(get_user_tenant_ids(auth.uid()))));

-- Create triggers for updated_at timestamps
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_tenants_updated_at BEFORE UPDATE ON tenants FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_team_members_updated_at BEFORE UPDATE ON team_members FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_user_profiles_updated_at BEFORE UPDATE ON user_profiles FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_forms_updated_at BEFORE UPDATE ON forms FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_submissions_updated_at BEFORE UPDATE ON submissions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_tenant_branding_updated_at BEFORE UPDATE ON tenant_branding FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_subscriptions_updated_at BEFORE UPDATE ON subscriptions FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default form templates
INSERT INTO form_templates (title, description, category, schema, is_public) VALUES
('Contact Form', 'Simple contact form with name, email, and message fields', 'General',
 '{"fields": [
   {"id": "name", "type": "text", "label": "Full Name", "required": true},
   {"id": "email", "type": "email", "label": "Email Address", "required": true},
   {"id": "message", "type": "textarea", "label": "Message", "required": true}
 ]}', true),
('Event Registration', 'Event registration form with attendee details', 'Events',
 '{"fields": [
   {"id": "name", "type": "text", "label": "Full Name", "required": true},
   {"id": "email", "type": "email", "label": "Email Address", "required": true},
   {"id": "phone", "type": "phone", "label": "Phone Number", "required": false},
   {"id": "dietary", "type": "select", "label": "Dietary Restrictions", "options": ["None", "Vegetarian", "Vegan", "Gluten-free"], "required": false}
 ]}', true),
('Customer Feedback', 'Customer satisfaction survey form', 'Survey',
 '{"fields": [
   {"id": "rating", "type": "radio", "label": "Overall Rating", "options": ["Excellent", "Good", "Average", "Poor"], "required": true},
   {"id": "feedback", "type": "textarea", "label": "Additional Comments", "required": false},
   {"id": "recommend", "type": "radio", "label": "Would you recommend us?", "options": ["Yes", "No"], "required": true}
 ]}', true);
