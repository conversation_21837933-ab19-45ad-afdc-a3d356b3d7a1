import { useState, useCallback, useRef } from 'react'
import { DndProvider } from 'react-dnd'
import { HTML5Backend } from 'react-dnd-html5-backend'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus, Save, Eye, Settings, Wand2, Undo, Redo } from 'lucide-react'
import { FormField, FormSchema, FieldType } from '@/types/form'
import { FieldPalette } from './FieldPalette'
import { FormCanvas } from './FormCanvas'
import { FieldPropertiesPanel } from './FieldPropertiesPanel'
import { FormSettingsPanel } from './FormSettingsPanel'
import { FormPreview } from './FormPreview'
import { AIFormGenerator } from './AIFormGenerator'
import { Button } from '@/components/ui/Button'
import { useFormBuilder } from '@/hooks/useFormBuilder'
import { useAuthStore } from '@/stores/authStore'
import { toast } from 'react-hot-toast'

interface FormBuilderProps {
  formId?: string
  initialSchema?: FormSchema
  onSave?: (schema: FormSchema) => void
  onPublish?: (schema: FormSchema) => void
}

export function FormBuilder({
  formId,
  initialSchema,
  onSave,
  onPublish
}: FormBuilderProps) {
  const { currentTenant, hasPermission } = useAuthStore()
  const {
    schema,
    selectedFieldId,
    history,
    canUndo,
    canRedo,
    addField,
    updateField,
    deleteField,
    moveField,
    selectField,
    updateFormSettings,
    undo,
    redo,
    saveForm,
    publishForm,
    isLoading
  } = useFormBuilder(formId, initialSchema)

  const [activePanel, setActivePanel] = useState<'fields' | 'properties' | 'settings' | 'ai'>('fields')
  const [showPreview, setShowPreview] = useState(false)
  const [isGeneratingWithAI, setIsGeneratingWithAI] = useState(false)

  const canvasRef = useRef<HTMLDivElement>(null)

  const handleAddField = useCallback((fieldType: FieldType, position?: { x: number; y: number }) => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to edit forms')
      return
    }

    const newField: Partial<FormField> = {
      type: fieldType,
      label: getDefaultLabel(fieldType),
      position: position || { x: 0, y: schema.fields.length * 80 },
      size: { width: 400, height: getDefaultHeight(fieldType) }
    }

    addField(newField)
    toast.success('Field added successfully')
  }, [addField, hasPermission, schema.fields.length])

  const handleUpdateField = useCallback((fieldId: string, updates: Partial<FormField>) => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to edit forms')
      return
    }

    updateField(fieldId, updates)
  }, [updateField, hasPermission])

  const handleDeleteField = useCallback((fieldId: string) => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to edit forms')
      return
    }

    deleteField(fieldId)
    toast.success('Field deleted successfully')
  }, [deleteField, hasPermission])

  const handleSave = useCallback(async () => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to save forms')
      return
    }

    try {
      await saveForm()
      onSave?.(schema)
      toast.success('Form saved successfully')
    } catch (error) {
      toast.error('Failed to save form')
      console.error('Save error:', error)
    }
  }, [saveForm, onSave, schema, hasPermission])

  const handlePublish = useCallback(async () => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to publish forms')
      return
    }

    try {
      await publishForm()
      onPublish?.(schema)
      toast.success('Form published successfully')
    } catch (error) {
      toast.error('Failed to publish form')
      console.error('Publish error:', error)
    }
  }, [publishForm, onPublish, schema, hasPermission])

  const handleAIGenerate = useCallback(async (prompt: string) => {
    if (!hasPermission('manage_forms')) {
      toast.error('You don\'t have permission to generate forms')
      return
    }

    setIsGeneratingWithAI(true)
    try {
      // TODO: Implement AI form generation
      // This would call an AI service to generate form fields based on the prompt
      toast.success('AI form generation coming soon!')
    } catch (error) {
      toast.error('Failed to generate form with AI')
      console.error('AI generation error:', error)
    } finally {
      setIsGeneratingWithAI(false)
    }
  }, [hasPermission])

  const selectedField = schema.fields.find(field => field.id === selectedFieldId)

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="h-screen flex flex-col bg-background">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-border bg-card">
          <div className="flex items-center space-x-4">
            <h1 className="text-xl font-semibold text-foreground">
              {schema.title || 'Untitled Form'}
            </h1>
            <div className="flex items-center space-x-2">
              <Button
                variant="ghost"
                size="sm"
                onClick={undo}
                disabled={!canUndo}
                className="p-2"
              >
                <Undo className="w-4 h-4" />
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={redo}
                disabled={!canRedo}
                className="p-2"
              >
                <Redo className="w-4 h-4" />
              </Button>
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => setShowPreview(true)}
              className="flex items-center space-x-2"
            >
              <Eye className="w-4 h-4" />
              <span>Preview</span>
            </Button>

            <Button
              variant="outline"
              size="sm"
              onClick={handleSave}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <Save className="w-4 h-4" />
              <span>Save</span>
            </Button>

            <Button
              size="sm"
              onClick={handlePublish}
              disabled={isLoading}
              className="flex items-center space-x-2"
            >
              <Plus className="w-4 h-4" />
              <span>Publish</span>
            </Button>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex overflow-hidden">
          {/* Left Sidebar */}
          <div className="w-80 border-r border-border bg-card flex flex-col">
            {/* Panel Tabs */}
            <div className="flex border-b border-border">
              {[
                { id: 'fields', label: 'Fields', icon: Plus },
                { id: 'properties', label: 'Properties', icon: Settings },
                { id: 'ai', label: 'AI Generate', icon: Wand2 }
              ].map(({ id, label, icon: Icon }) => (
                <button
                  key={id}
                  onClick={() => setActivePanel(id as any)}
                  className={`flex-1 flex items-center justify-center space-x-2 py-3 px-4 text-sm font-medium transition-colors ${
                    activePanel === id
                      ? 'bg-primary text-primary-foreground'
                      : 'text-muted-foreground hover:text-foreground hover:bg-muted'
                  }`}
                >
                  <Icon className="w-4 h-4" />
                  <span>{label}</span>
                </button>
              ))}
            </div>

            {/* Panel Content */}
            <div className="flex-1 overflow-y-auto">
              <AnimatePresence mode="wait">
                {activePanel === 'fields' && (
                  <motion.div
                    key="fields"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FieldPalette onAddField={handleAddField} />
                  </motion.div>
                )}

                {activePanel === 'properties' && (
                  <motion.div
                    key="properties"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <FieldPropertiesPanel
                      field={selectedField}
                      onUpdateField={handleUpdateField}
                      onDeleteField={handleDeleteField}
                    />
                  </motion.div>
                )}

                {activePanel === 'ai' && (
                  <motion.div
                    key="ai"
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    exit={{ opacity: 0, x: -20 }}
                    transition={{ duration: 0.2 }}
                  >
                    <AIFormGenerator
                      onGenerate={handleAIGenerate}
                      isGenerating={isGeneratingWithAI}
                    />
                  </motion.div>
                )}
              </AnimatePresence>
            </div>
          </div>

          {/* Form Canvas */}
          <div className="flex-1 overflow-hidden">
            <FormCanvas
              ref={canvasRef}
              schema={schema}
              selectedFieldId={selectedFieldId}
              onSelectField={selectField}
              onUpdateField={handleUpdateField}
              onDeleteField={handleDeleteField}
              onMoveField={moveField}
              onAddField={handleAddField}
            />
          </div>

          {/* Right Sidebar - Form Settings */}
          <div className="w-80 border-l border-border bg-card">
            <FormSettingsPanel
              settings={schema.settings}
              onUpdateSettings={updateFormSettings}
            />
          </div>
        </div>

        {/* Preview Modal */}
        <AnimatePresence>
          {showPreview && (
            <FormPreview
              schema={schema}
              onClose={() => setShowPreview(false)}
            />
          )}
        </AnimatePresence>
      </div>
    </DndProvider>
  )
}

// Helper functions
function getDefaultLabel(fieldType: FieldType): string {
  const labels: Record<FieldType, string> = {
    text: 'Text Input',
    textarea: 'Text Area',
    email: 'Email Address',
    phone: 'Phone Number',
    number: 'Number',
    url: 'Website URL',
    password: 'Password',
    select: 'Dropdown',
    radio: 'Multiple Choice',
    checkbox: 'Checkboxes',
    multi_select: 'Multi Select',
    date: 'Date',
    time: 'Time',
    datetime: 'Date & Time',
    file: 'File Upload',
    image: 'Image Upload',
    signature: 'Signature',
    rating: 'Rating',
    slider: 'Slider',
    payment: 'Payment',
    divider: 'Divider',
    heading: 'Heading',
    paragraph: 'Paragraph',
    html: 'HTML Content'
  }
  return labels[fieldType] || 'Field'
}

function getDefaultHeight(fieldType: FieldType): number {
  const heights: Record<FieldType, number> = {
    text: 60,
    textarea: 120,
    email: 60,
    phone: 60,
    number: 60,
    url: 60,
    password: 60,
    select: 60,
    radio: 120,
    checkbox: 120,
    multi_select: 120,
    date: 60,
    time: 60,
    datetime: 60,
    file: 80,
    image: 80,
    signature: 150,
    rating: 60,
    slider: 80,
    payment: 100,
    divider: 20,
    heading: 40,
    paragraph: 80,
    html: 100
  }
  return heights[fieldType] || 60
}
