import { useDrag } from 'react-dnd'
import { motion } from 'framer-motion'
import {
  Type,
  AlignLeft,
  Mail,
  Phone,
  Hash,
  Link,
  Lock,
  ChevronDown,
  Circle,
  Square,
  Calendar,
  Clock,
  Upload,
  Image,
  PenTool,
  Star,
  Sliders,
  CreditCard,
  Minus,
  Heading1,
  FileText,
  Code
} from 'lucide-react'
import { FieldType, FieldTemplate } from '@/types/form'

interface FieldPaletteProps {
  onAddField: (fieldType: FieldType) => void
}

const fieldTemplates: FieldTemplate[] = [
  // Input Fields
  { type: 'text', label: 'Text Input', icon: 'Type', category: 'input', defaultSettings: {} },
  { type: 'textarea', label: 'Text Area', icon: 'AlignLeft', category: 'input', defaultSettings: {} },
  { type: 'email', label: 'Email', icon: 'Mail', category: 'input', defaultSettings: {} },
  { type: 'phone', label: 'Phone', icon: 'Phone', category: 'input', defaultSettings: {} },
  { type: 'number', label: 'Number', icon: 'Hash', category: 'input', defaultSettings: {} },
  { type: 'url', label: 'URL', icon: 'Link', category: 'input', defaultSettings: {} },
  { type: 'password', label: 'Password', icon: 'Lock', category: 'input', defaultSettings: {} },

  // Choice Fields
  { type: 'select', label: 'Dropdown', icon: 'ChevronDown', category: 'choice', defaultSettings: {} },
  { type: 'radio', label: 'Radio Buttons', icon: 'Circle', category: 'choice', defaultSettings: {} },
  { type: 'checkbox', label: 'Checkboxes', icon: 'Square', category: 'choice', defaultSettings: {} },
  { type: 'multi_select', label: 'Multi Select', icon: 'Square', category: 'choice', defaultSettings: {} },

  // Date & Time
  { type: 'date', label: 'Date', icon: 'Calendar', category: 'input', defaultSettings: {} },
  { type: 'time', label: 'Time', icon: 'Clock', category: 'input', defaultSettings: {} },
  { type: 'datetime', label: 'Date & Time', icon: 'Calendar', category: 'input', defaultSettings: {} },

  // File & Media
  { type: 'file', label: 'File Upload', icon: 'Upload', category: 'input', defaultSettings: {} },
  { type: 'image', label: 'Image Upload', icon: 'Image', category: 'input', defaultSettings: {} },
  { type: 'signature', label: 'Signature', icon: 'PenTool', category: 'input', defaultSettings: {} },

  // Advanced
  { type: 'rating', label: 'Rating', icon: 'Star', category: 'advanced', defaultSettings: {} },
  { type: 'slider', label: 'Slider', icon: 'Sliders', category: 'advanced', defaultSettings: {} },
  { type: 'payment', label: 'Payment', icon: 'CreditCard', category: 'advanced', defaultSettings: {} },

  // Layout
  { type: 'divider', label: 'Divider', icon: 'Minus', category: 'layout', defaultSettings: {} },
  { type: 'heading', label: 'Heading', icon: 'Heading1', category: 'layout', defaultSettings: {} },
  { type: 'paragraph', label: 'Paragraph', icon: 'FileText', category: 'layout', defaultSettings: {} },
  { type: 'html', label: 'HTML', icon: 'Code', category: 'layout', defaultSettings: {} }
]

const iconMap = {
  Type, AlignLeft, Mail, Phone, Hash, Link, Lock, ChevronDown, Circle, Square,
  Calendar, Clock, Upload, Image, PenTool, Star, Sliders, CreditCard, Minus,
  Heading1, FileText, Code
}

interface DraggableFieldProps {
  template: FieldTemplate
  onAddField: (fieldType: FieldType) => void
}

function DraggableField({ template, onAddField }: DraggableFieldProps) {
  const [{ isDragging }, drag] = useDrag(() => ({
    type: 'field',
    item: { type: 'field', fieldType: template.type },
    collect: (monitor) => ({
      isDragging: monitor.isDragging()
    })
  }))

  const IconComponent = iconMap[template.icon as keyof typeof iconMap]

  return (
    <motion.div
      ref={drag}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      className={`
        flex items-center space-x-3 p-3 rounded-lg border border-border bg-card
        hover:bg-accent hover:border-accent-foreground/20 cursor-grab
        transition-all duration-200
        ${isDragging ? 'opacity-50 rotate-2' : ''}
      `}
      onClick={() => onAddField(template.type)}
    >
      <div className="flex-shrink-0 w-8 h-8 flex items-center justify-center rounded-md bg-primary/10 text-primary">
        <IconComponent className="w-4 h-4" />
      </div>
      <div className="flex-1 min-w-0">
        <p className="text-sm font-medium text-foreground truncate">
          {template.label}
        </p>
      </div>
    </motion.div>
  )
}

export function FieldPalette({ onAddField }: FieldPaletteProps) {
  const categories = [
    { id: 'input', label: 'Input Fields', fields: fieldTemplates.filter(f => f.category === 'input') },
    { id: 'choice', label: 'Choice Fields', fields: fieldTemplates.filter(f => f.category === 'choice') },
    { id: 'advanced', label: 'Advanced', fields: fieldTemplates.filter(f => f.category === 'advanced') },
    { id: 'layout', label: 'Layout', fields: fieldTemplates.filter(f => f.category === 'layout') }
  ]

  return (
    <div className="p-4 space-y-6">
      <div className="text-center">
        <h3 className="text-lg font-semibold text-foreground mb-2">Form Fields</h3>
        <p className="text-sm text-muted-foreground">
          Drag fields to the canvas or click to add
        </p>
      </div>

      {categories.map((category) => (
        <div key={category.id} className="space-y-3">
          <h4 className="text-sm font-medium text-foreground uppercase tracking-wide">
            {category.label}
          </h4>
          <div className="space-y-2">
            {category.fields.map((template) => (
              <DraggableField
                key={template.type}
                template={template}
                onAddField={onAddField}
              />
            ))}
          </div>
        </div>
      ))}
    </div>
  )
}
