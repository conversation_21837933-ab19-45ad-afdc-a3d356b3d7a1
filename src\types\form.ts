export type FieldType =
  | 'text'
  | 'textarea'
  | 'email'
  | 'phone'
  | 'number'
  | 'url'
  | 'password'
  | 'select'
  | 'radio'
  | 'checkbox'
  | 'multi_select'
  | 'date'
  | 'time'
  | 'datetime'
  | 'file'
  | 'image'
  | 'signature'
  | 'rating'
  | 'slider'
  | 'payment'
  | 'divider'
  | 'heading'
  | 'paragraph'
  | 'html'

export interface FormFieldOption {
  id: string
  label: string
  value: string
  color?: string
}

export interface FormFieldValidation {
  required?: boolean
  minLength?: number
  maxLength?: number
  min?: number
  max?: number
  pattern?: string
  customMessage?: string
}

export interface FormFieldConditional {
  fieldId: string
  operator: 'equals' | 'not_equals' | 'contains' | 'not_contains' | 'greater_than' | 'less_than'
  value: string | number | boolean
  action: 'show' | 'hide' | 'require' | 'skip'
}

export interface FormField {
  id: string
  type: FieldType
  label: string
  placeholder?: string
  description?: string
  required?: boolean
  validation?: FormFieldValidation
  options?: FormFieldOption[]
  defaultValue?: string | number | boolean | string[]
  conditionals?: FormFieldConditional[]
  settings?: {
    // Text fields
    multiline?: boolean
    rows?: number

    // Number fields
    step?: number

    // File fields
    accept?: string
    maxSize?: number
    multiple?: boolean

    // Rating fields
    maxRating?: number
    icon?: 'star' | 'heart' | 'thumbs'

    // Slider fields
    minLabel?: string
    maxLabel?: string

    // Payment fields
    currency?: string
    amount?: number
    allowCustomAmount?: boolean

    // Layout fields
    size?: 'small' | 'medium' | 'large'
    alignment?: 'left' | 'center' | 'right'

    // Advanced
    customCSS?: string
    customAttributes?: Record<string, string>
  }
  position: {
    x: number
    y: number
  }
  size: {
    width: number
    height: number
  }
}

export interface FormSettings {
  title: string
  description?: string
  theme: {
    primaryColor: string
    backgroundColor: string
    textColor: string
    fontFamily: string
    borderRadius: number
  }
  behavior: {
    allowMultipleSubmissions: boolean
    requireLogin: boolean
    showProgressBar: boolean
    saveProgress: boolean
    collectIPAddress: boolean
    collectUserAgent: boolean
  }
  notifications: {
    sendConfirmationEmail: boolean
    confirmationEmailTemplate?: string
    notifyOnSubmission: boolean
    notificationEmails: string[]
  }
  integrations: {
    googleSheets?: {
      spreadsheetId: string
      worksheetName: string
    }
    slack?: {
      webhookUrl: string
      channel: string
    }
    zapier?: {
      webhookUrl: string
    }
  }
  payment?: {
    enabled: boolean
    provider: 'stripe' | 'paypal'
    currency: string
    amount?: number
    allowCustomAmount: boolean
    description: string
  }
  security: {
    enableCaptcha: boolean
    enableHoneypot: boolean
    limitSubmissions: boolean
    maxSubmissionsPerIP?: number
    allowedDomains?: string[]
  }
  seo: {
    metaTitle?: string
    metaDescription?: string
    ogImage?: string
  }
}

export interface FormSchema {
  id: string
  title: string
  description?: string
  fields: FormField[]
  settings: FormSettings
  version: number
  createdAt: string
  updatedAt: string
}

export interface FormTemplate {
  id: string
  title: string
  description: string
  category: string
  tags: string[]
  schema: Omit<FormSchema, 'id' | 'createdAt' | 'updatedAt'>
  previewImage?: string
  isPublic: boolean
  usageCount: number
}

export interface FormSubmission {
  id: string
  formId: string
  data: Record<string, any>
  metadata: {
    ipAddress?: string
    userAgent?: string
    referrer?: string
    submittedAt: string
    completionTime?: number
  }
  status: 'draft' | 'completed' | 'abandoned'
  paymentStatus?: 'pending' | 'completed' | 'failed' | 'refunded'
}

export interface FormAnalytics {
  formId: string
  totalViews: number
  totalSubmissions: number
  completionRate: number
  averageCompletionTime: number
  abandonmentPoints: Array<{
    fieldId: string
    abandonmentRate: number
  }>
  deviceBreakdown: {
    desktop: number
    mobile: number
    tablet: number
  }
  sourceBreakdown: Record<string, number>
  conversionFunnel: Array<{
    step: string
    visitors: number
    conversionRate: number
  }>
}

// Form Builder specific types
export interface FieldTemplate {
  type: FieldType
  label: string
  icon: string
  category: 'input' | 'choice' | 'layout' | 'advanced'
  defaultSettings: Partial<FormField>
}

export interface DragItem {
  type: 'field'
  fieldType: FieldType
  id?: string
}

export interface DropResult {
  dragIndex: number
  hoverIndex: number
}
