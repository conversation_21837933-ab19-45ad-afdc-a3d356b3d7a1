import { Routes, Route, Navigate } from 'react-router-dom'
import { AuthProvider } from '@/components/auth/AuthProvider'
import { ProtectedRoute } from '@/components/auth/ProtectedRoute'
import { Layout } from '@/components/layout/Layout'

// Pages
import { LoginPage } from '@/pages/auth/LoginPage'
import { SignupPage } from '@/pages/auth/SignupPage'
import { DashboardPage } from '@/pages/dashboard/DashboardPage'
import { FormsPage } from '@/pages/forms/FormsPage'
import { FormBuilderPage } from '@/pages/forms/FormBuilderPage'
import { FormViewPage } from '@/pages/forms/FormViewPage'
import { SubmissionsPage } from '@/pages/submissions/SubmissionsPage'
import { AnalyticsPage } from '@/pages/analytics/AnalyticsPage'
import { IntegrationsPage } from '@/pages/integrations/IntegrationsPage'
import { WorkflowsPage } from '@/pages/workflows/WorkflowsPage'
import { SettingsPage } from '@/pages/settings/SettingsPage'
import { BillingPage } from '@/pages/billing/BillingPage'
import { TeamPage } from '@/pages/team/TeamPage'

function App() {
  return (
    <AuthProvider>
      <div className="min-h-screen bg-background">
        <Routes>
          {/* Public Routes */}
          <Route path="/login" element={<LoginPage />} />
          <Route path="/signup" element={<SignupPage />} />
          <Route path="/form/:formId" element={<FormViewPage />} />

          {/* Protected Routes */}
          <Route path="/" element={
            <ProtectedRoute>
              <Layout />
            </ProtectedRoute>
          }>
            <Route index element={<Navigate to="/dashboard" replace />} />
            <Route path="dashboard" element={<DashboardPage />} />
            <Route path="forms" element={<FormsPage />} />
            <Route path="forms/new" element={<FormBuilderPage />} />
            <Route path="forms/:formId/edit" element={<FormBuilderPage />} />
            <Route path="forms/:formId/submissions" element={<SubmissionsPage />} />
            <Route path="forms/:formId/analytics" element={<AnalyticsPage />} />
            <Route path="integrations" element={<IntegrationsPage />} />
            <Route path="workflows" element={<WorkflowsPage />} />
            <Route path="settings" element={<SettingsPage />} />
            <Route path="billing" element={<BillingPage />} />
            <Route path="team" element={<TeamPage />} />
          </Route>

          {/* Catch all */}
          <Route path="*" element={<Navigate to="/dashboard" replace />} />
        </Routes>
      </div>
    </AuthProvider>
  )
}

export default App
