// DocuFill Application - Fixed Implementation
class DocuFillApp {
    constructor() {
        this.currentUser = null;
        this.currentPage = 'dashboard';
        this.visitorDocumentCount = 0;
        this.maxVisitorDocuments = 5;
        this.uploadedTemplates = [];
        this.pendingTemplate = null;
        this.init();
    }

    init() {
        this.loadApplicationData();
        this.setupApp();
    }

    loadApplicationData() {
        this.data = {
            users: [
                {
                    id: 1,
                    name: "<PERSON><PERSON>",
                    email: "<EMAIL>",
                    role: "super_admin",
                    organization: "Central Railway",
                    department: "Administration",
                    designation: "Chief Administrator",
                    status: "Active",
                    permissions: ["all"],
                    templatePermissions: ["create", "edit", "delete", "approve", "publish", "manage_categories"],
                    canManageUsers: true,
                    canManageOrganizations: true,
                    templatesCreated: 15,
                    documentsGenerated: 89
                },
                {
                    id: 2,
                    name: "<PERSON><PERSON>", 
                    email: "<EMAIL>",
                    role: "admin",
                    organization: "Central Railway",
                    department: "Finance",
                    designation: "Finance Manager",
                    status: "Active",
                    permissions: ["template_manage", "user_manage", "organization_settings", "analytics_view"],
                    templatePermissions: ["create", "edit", "approve", "publish"],
                    canManageUsers: true,
                    canManageOrganizations: false,
                    templatesCreated: 12,
                    documentsGenerated: 156
                },
                {
                    id: 3,
                    name: "Amit Singh",
                    email: "<EMAIL>", 
                    role: "supervisor",
                    organization: "Central Railway",
                    department: "Operations",
                    designation: "Station Superintendent",
                    status: "Active",
                    permissions: ["template_view", "document_generate", "document_approve", "team_manage", "department_templates"],
                    templatePermissions: ["create", "edit", "approve"],
                    canManageUsers: false,
                    canManageOrganizations: false,
                    templatesCreated: 8,
                    documentsGenerated: 234
                },
                {
                    id: 4,
                    name: "Sunita Patel",
                    email: "<EMAIL>",
                    role: "employee", 
                    organization: "Central Railway",
                    department: "Commercial",
                    designation: "Booking Clerk",
                    status: "Active",
                    permissions: ["template_view", "document_generate", "personal_templates", "document_save"],
                    templatePermissions: ["create", "edit"],
                    canManageUsers: false,
                    canManageOrganizations: false,
                    templatesCreated: 3,
                    documentsGenerated: 67
                },
                {
                    id: 5,
                    name: "Visitor User",
                    email: "<EMAIL>",
                    role: "visitor",
                    organization: "Guest",
                    department: "N/A",
                    designation: "Visitor",
                    status: "Active",
                    permissions: ["template_view", "basic_document_generate"],
                    templatePermissions: [],
                    canManageUsers: false,
                    canManageOrganizations: false,
                    templatesCreated: 0,
                    documentsGenerated: 0,
                    sessionLimits: {
                        maxDocuments: 5,
                        sessionDuration: 24
                    }
                }
            ],
            templateCategories: [
                {
                    id: 1,
                    name: "Railway Operations",
                    description: "Templates for railway operations and maintenance",
                    icon: "🚂",
                    color: "#2563eb", 
                    templates: 18,
                    subcategories: ["Failure Reports", "Maintenance Logs", "Inspection Reports", "Safety Documents"]
                },
                {
                    id: 2,
                    name: "Financial & HR",
                    description: "Financial forms and HR documentation", 
                    icon: "💰",
                    color: "#16a34a",
                    templates: 15,
                    subcategories: ["Travel Allowance", "Expense Reports", "Leave Applications", "Payroll Forms"]
                },
                {
                    id: 3,
                    name: "Compliance & Legal",
                    description: "Legal and compliance documentation",
                    icon: "🛡️", 
                    color: "#dc2626",
                    templates: 8,
                    subcategories: ["Audit Reports", "Compliance Forms", "Legal Documents", "Certificates"]
                },
                {
                    id: 4,
                    name: "Administrative",
                    description: "General administrative templates",
                    icon: "📋",
                    color: "#7c3aed",
                    templates: 4,
                    subcategories: ["Memos", "Letters", "Reports", "Notifications"]
                }
            ],
            templates: [
                {
                    id: 1,
                    name: "Loco Failure Report",
                    description: "Comprehensive locomotive failure and detention reporting template",
                    category: "Railway Operations",
                    subcategory: "Failure Reports",
                    version: "1.2",
                    status: "Published",
                    created: "2025-08-20T10:00:00Z",
                    lastModified: "2025-08-25T15:30:00Z",
                    createdBy: "Admin User",
                    approvedBy: "System Admin",
                    usageCount: 156,
                    rating: 4.8,
                    tags: ["railway", "operations", "failure", "report", "locomotive"],
                    visitorAccess: true,
                    isPublic: true,
                    fileFormats: ["pdf", "doc"],
                    fields: [
                        {name: "LocoFailureOrDetention", type: "select", label: "Failure/Detention Type", required: true, options: ["Engine Failure", "Signal Failure", "Track Issue", "Other"]},
                        {name: "Date", type: "date", label: "Incident Date", required: true},
                        {name: "LocoNo", type: "text", label: "Locomotive Number", required: true},
                        {name: "Shed", type: "text", label: "Shed", required: true},
                        {name: "SchDone", type: "text", label: "Schedule Done", required: true},
                        {name: "SchDue", type: "text", label: "Schedule Due", required: true},
                        {name: "TrainNo", type: "text", label: "Train Number", required: true},
                        {name: "Load", type: "text", label: "Load", required: true},
                        {name: "LPM", type: "text", label: "Loco Pilot Name", required: true},
                        {name: "ALP", type: "text", label: "Assistant Loco Pilot Name", required: true},
                        {name: "Section", type: "text", label: "Section", required: true},
                        {name: "Station", type: "text", label: "Station", required: true},
                        {name: "BriefHistory", type: "textarea", label: "Brief History", required: true},
                        {name: "Investigation", type: "textarea", label: "Investigation and Observation", required: true},
                        {name: "Conclusion", type: "textarea", label: "Conclusion", required: true},
                        {name: "Responsibility", type: "textarea", label: "Responsibility", required: true},
                        {name: "Supervisor1", type: "text", label: "Supervisor 1 Signature", required: true},
                        {name: "Supervisor2", type: "text", label: "Supervisor 2 Signature", required: true}
                    ],
                    content: `LOCOMOTIVE FAILURE/DETENTION REPORT

Failure/Detention Type: {{LocoFailureOrDetention}}
Date: {{Date}}
Locomotive Number: {{LocoNo}}        Shed: {{Shed}}        Schedule Done: {{SchDone}}        Schedule Due: {{SchDue}}
Train Number: {{TrainNo}}  Load: {{Load}}        Loco Pilot: {{LPM}}                   Assistant LP: {{ALP}}

Section: {{Section}}    Station: {{Station}}

Brief History: {{BriefHistory}}

Investigation and Observation: {{Investigation}}

Conclusion: {{Conclusion}}

Responsibility: {{Responsibility}}

Supervisor 1: {{Supervisor1}}                    Supervisor 2: {{Supervisor2}}`
                },
                {
                    id: 2,
                    name: "Travel Allowance Form",
                    description: "Official travel allowance claim form for railway employees",
                    category: "Financial & HR",
                    subcategory: "Travel Allowance",
                    version: "2.1",
                    status: "Published", 
                    created: "2025-08-18T09:00:00Z",
                    lastModified: "2025-08-24T14:20:00Z",
                    createdBy: "Finance Admin",
                    approvedBy: "Finance Manager",
                    usageCount: 289,
                    rating: 4.6,
                    tags: ["finance", "travel", "allowance", "reimbursement"],
                    visitorAccess: true,
                    isPublic: true,
                    fileFormats: ["pdf", "doc"],
                    useTableLayout: true,
                    fields: [
                        {name: "Branch", type: "text", label: "Branch", required: true},
                        {name: "Division", type: "text", label: "Division", required: true},
                        {name: "Headquarter", type: "text", label: "Headquarter", required: true},
                        {name: "NameofEmployee", type: "text", label: "Employee Name", required: true},
                        {name: "Month", type: "text", label: "Claim Month", required: true},
                        {name: "Designation", type: "text", label: "Designation", required: true},
                        {name: "Basic", type: "number", label: "Basic Pay", required: true},
                        {name: "GradePay", type: "number", label: "Grade Pay", required: true},
                        {name: "PFNo", type: "text", label: "PF Number", required: true},
                        {name: "MobileNo", type: "tel", label: "Mobile Number", required: true},
                        {name: "MonthandDate1", type: "text", label: "Month & Date 1", required: true},
                        {name: "TrainNo1", type: "text", label: "Train No 1", required: true},
                        {name: "TimeLeft1", type: "time", label: "Time Left 1", required: true},
                        {name: "TimeArrived1", type: "time", label: "Time Arrived 1", required: true},
                        {name: "StationFrom1", type: "text", label: "Station From 1", required: true},
                        {name: "StationTo1", type: "text", label: "Station To 1", required: true},
                        {name: "Kms1", type: "number", label: "Kilometers 1", required: true},
                        {name: "DayNight1", type: "select", label: "Day/Night 1", options: ["Day", "Night"], required: true},
                        {name: "ObjectofJourney1", type: "text", label: "Object of Journey 1", required: true},
                        {name: "Rate1", type: "number", label: "Rate 1", required: true},
                        {name: "TotalWords", type: "text", label: "Total in Words", required: true},
                        {name: "Total", type: "number", label: "Total Amount", required: true},
                        {name: "Sign", type: "text", label: "Signature", required: true}
                    ]
                }
            ],
            documents: [
                {
                    id: 1,
                    template: "Loco Failure Report",
                    generatedBy: "Amit Singh",
                    date: "2025-08-25",
                    status: "Completed",
                    category: "Railway Operations"
                },
                {
                    id: 2,
                    template: "Travel Allowance Form",
                    generatedBy: "Priya Sharma",
                    date: "2025-08-24",
                    status: "Pending",
                    category: "Financial & HR"
                }
            ],
            organizations: [
                {
                    id: 1,
                    name: "Central Railway",
                    code: "CR",
                    division: "Mumbai Division",
                    totalUsers: 1250,
                    activeUsers: 1180,
                    totalTemplates: 45,
                    totalDocuments: 15680,
                    status: "Active",
                    tier: "Enterprise"
                }
            ]
        };
    }

    setupApp() {
        this.bindLoginEvents();
        this.showLoginPage();
        
        // Make app globally available
        window.app = this;
    }

    bindLoginEvents() {
        // Login form
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleLogin();
            });
        }

        // Visitor access
        const visitorBtn = document.getElementById('visitorAccessBtn');
        if (visitorBtn) {
            visitorBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.loginAsVisitor();
            });
        }

        // Demo buttons
        document.querySelectorAll('.demo-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const email = btn.getAttribute('data-email');
                const role = btn.getAttribute('data-role');
                this.loginWithDemo(email, role);
            });
        });

        // Modal events
        this.bindModalEvents();
    }

    bindModalEvents() {
        // Modal close buttons
        document.querySelectorAll('.modal-close').forEach(btn => {
            btn.addEventListener('click', (e) => {
                e.preventDefault();
                const modal = e.target.closest('.modal');
                this.closeModal(modal);
            });
        });

        // Modal backdrop click
        document.querySelectorAll('.modal').forEach(modal => {
            modal.addEventListener('click', (e) => {
                if (e.target === modal) {
                    this.closeModal(modal);
                }
            });
        });

        // Create account
        const createAccountLink = document.getElementById('createAccountLink');
        if (createAccountLink) {
            createAccountLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showModal('createAccountModal');
            });
        }

        // Forgot password
        const forgotPasswordLink = document.getElementById('forgotPasswordLink');
        if (forgotPasswordLink) {
            forgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.showModal('forgotPasswordModal');
            });
        }

        // Forms
        const createAccountForm = document.getElementById('createAccountForm');
        if (createAccountForm) {
            createAccountForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCreateAccount();
            });
        }

        const forgotPasswordForm = document.getElementById('forgotPasswordForm');
        if (forgotPasswordForm) {
            forgotPasswordForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleForgotPassword();
            });
        }
    }

    handleLogin() {
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;

        if (!email || !password) {
            this.showMessage('Please enter both email and password', 'error');
            return;
        }

        const user = this.data.users.find(u => u.email === email);
        if (user) {
            this.currentUser = user;
            this.showMainApp();
        } else {
            this.showMessage('Invalid credentials. Try using demo accounts or continue as visitor.', 'error');
        }
    }

    loginAsVisitor() {
        this.currentUser = this.data.users.find(u => u.role === 'visitor');
        this.visitorDocumentCount = 0;
        this.showMainApp();
    }

    loginWithDemo(email, role) {
        const user = this.data.users.find(u => u.email === email);
        if (user) {
            this.currentUser = user;
            this.showMainApp();
        } else {
            this.showMessage('Demo user not found', 'error');
        }
    }

    handleCreateAccount() {
        const formData = {
            name: document.getElementById('regName').value,
            email: document.getElementById('regEmail').value,
            password: document.getElementById('regPassword').value,
            confirmPassword: document.getElementById('regConfirmPassword').value,
            organization: document.getElementById('regOrganization').value,
            role: document.getElementById('regRole').value
        };

        // Validation
        if (!formData.name || !formData.email || !formData.password || !formData.organization || !formData.role) {
            this.showMessage('Please fill in all fields', 'error');
            return;
        }

        if (formData.password !== formData.confirmPassword) {
            this.showMessage('Passwords do not match', 'error');
            return;
        }

        if (this.data.users.find(u => u.email === formData.email)) {
            this.showMessage('User with this email already exists', 'error');
            return;
        }

        // Create user
        const newUser = {
            id: Date.now(),
            name: formData.name,
            email: formData.email,
            role: formData.role,
            organization: formData.organization,
            department: 'New Department',
            designation: 'New Employee',
            status: 'Active',
            permissions: this.getPermissionsByRole(formData.role),
            templatePermissions: this.getTemplatePermissionsByRole(formData.role),
            canManageUsers: false,
            canManageOrganizations: false,
            templatesCreated: 0,
            documentsGenerated: 0
        };

        this.data.users.push(newUser);
        this.closeModal(document.getElementById('createAccountModal'));
        this.showMessage('Account created successfully! You can now sign in.', 'success');
        document.getElementById('createAccountForm').reset();
    }

    handleForgotPassword() {
        const email = document.getElementById('resetEmail').value;
        if (!email) {
            this.showMessage('Please enter your email address', 'error');
            return;
        }

        this.closeModal(document.getElementById('forgotPasswordModal'));
        this.showMessage(`Password reset link sent to ${email}`, 'success');
        document.getElementById('forgotPasswordForm').reset();
    }

    getPermissionsByRole(role) {
        const rolePermissions = {
            visitor: ["template_view", "basic_document_generate"],
            employee: ["template_view", "document_generate", "personal_templates", "document_save"],
            supervisor: ["template_view", "document_generate", "document_approve", "team_manage", "department_templates"],
            admin: ["template_manage", "user_manage", "organization_settings", "analytics_view"],
            super_admin: ["all"]
        };
        return rolePermissions[role] || [];
    }

    getTemplatePermissionsByRole(role) {
        const templatePermissions = {
            visitor: [],
            employee: ["create", "edit"],
            supervisor: ["create", "edit", "approve"],
            admin: ["create", "edit", "approve", "publish"],
            super_admin: ["create", "edit", "delete", "approve", "publish", "manage_categories"]
        };
        return templatePermissions[role] || [];
    }

    showLoginPage() {
        const loginPage = document.getElementById('loginPage');
        const mainApp = document.getElementById('mainApp');
        
        if (loginPage) loginPage.classList.add('active');
        if (mainApp) mainApp.classList.remove('active');
    }

    showMainApp() {
        const loginPage = document.getElementById('loginPage');
        const mainApp = document.getElementById('mainApp');
        
        if (loginPage) loginPage.classList.remove('active');
        if (mainApp) mainApp.classList.add('active');
        
        this.updateUserInterface();
        this.bindMainAppEvents();
        this.showPage('dashboard');
    }

    updateUserInterface() {
        const userName = document.getElementById('userName');
        const userRole = document.getElementById('userRole');
        const visitorLimits = document.getElementById('visitorLimits');
        
        if (userName) userName.textContent = this.currentUser.name;
        if (userRole) {
            userRole.textContent = this.currentUser.role.replace('_', ' ').toUpperCase();
            userRole.className = `user-role ${this.currentUser.role}`;
        }

        // Show visitor limits
        if (this.currentUser.role === 'visitor') {
            if (visitorLimits) {
                visitorLimits.classList.remove('hidden');
                this.updateVisitorLimits();
            }
        } else {
            if (visitorLimits) visitorLimits.classList.add('hidden');
        }

        // Show/hide role-specific elements
        this.updateRoleVisibility();
    }

    updateRoleVisibility() {
        const adminElements = document.querySelectorAll('.admin-only');
        const superAdminElements = document.querySelectorAll('.super-admin-only');
        const navLinks = document.querySelectorAll('.nav-link[data-min-role]');

        // Admin elements
        if (this.hasPermission('user_manage') || this.currentUser.role === 'super_admin') {
            adminElements.forEach(el => el.classList.add('show'));
        } else {
            adminElements.forEach(el => el.classList.remove('show'));
        }

        // Super admin elements
        if (this.currentUser.role === 'super_admin') {
            superAdminElements.forEach(el => el.classList.add('show'));
        } else {
            superAdminElements.forEach(el => el.classList.remove('show'));
        }

        // Role-based navigation
        navLinks.forEach(link => {
            const minRole = link.getAttribute('data-min-role');
            if (this.hasMinimumRole(minRole)) {
                link.parentElement.style.display = 'block';
            } else {
                link.parentElement.style.display = 'none';
            }
        });
    }

    updateVisitorLimits() {
        const documentsLeft = document.getElementById('documentsLeft');
        if (documentsLeft) {
            const remaining = this.maxVisitorDocuments - this.visitorDocumentCount;
            documentsLeft.textContent = Math.max(0, remaining);
        }
    }

    hasPermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.permissions.includes('all') || this.currentUser.permissions.includes(permission);
    }

    hasMinimumRole(requiredRole) {
        const roleHierarchy = ['visitor', 'employee', 'supervisor', 'admin', 'super_admin'];
        const currentRoleIndex = roleHierarchy.indexOf(this.currentUser.role);
        const requiredRoleIndex = roleHierarchy.indexOf(requiredRole);
        return currentRoleIndex >= requiredRoleIndex;
    }

    bindMainAppEvents() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const page = e.target.getAttribute('data-page');
                if (page) {
                    this.showPage(page);
                } else if (e.target.classList.contains('logout-link')) {
                    this.logout();
                }
            });
        });

        // Template upload events
        this.bindUploadEvents();
        
        // Document generation events
        this.bindDocumentEvents();
    }

    bindUploadEvents() {
        const uploadForm = document.getElementById('templateUploadForm');
        const fileInput = document.getElementById('templateFile');
        const fileDropZone = document.getElementById('fileDropZone');
        const browseBtn = document.getElementById('browseFileBtn');
        const previewBtn = document.getElementById('previewTemplateBtn');

        if (uploadForm) {
            uploadForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleTemplateUpload();
            });
        }

        if (browseBtn && fileInput) {
            browseBtn.addEventListener('click', (e) => {
                e.preventDefault();
                fileInput.click();
            });
        }

        if (fileInput) {
            fileInput.addEventListener('change', (e) => {
                const file = e.target.files[0];
                if (file) {
                    this.handleFileSelection(file);
                }
            });
        }

        if (fileDropZone) {
            fileDropZone.addEventListener('dragover', (e) => {
                e.preventDefault();
                fileDropZone.classList.add('dragover');
            });

            fileDropZone.addEventListener('dragleave', () => {
                fileDropZone.classList.remove('dragover');
            });

            fileDropZone.addEventListener('drop', (e) => {
                e.preventDefault();
                fileDropZone.classList.remove('dragover');
                const file = e.dataTransfer.files[0];
                if (file) {
                    this.handleFileSelection(file);
                }
            });

            fileDropZone.addEventListener('click', () => {
                if (fileInput) fileInput.click();
            });
        }

        if (previewBtn) {
            previewBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.previewTemplate();
            });
        }

        // Publish template button in modal
        const publishBtn = document.getElementById('publishTemplateBtn');
        if (publishBtn) {
            publishBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.publishTemplate();
            });
        }
    }

    bindDocumentEvents() {
        const docTemplateSelect = document.getElementById('docTemplateSelect');
        const generateBtn = document.getElementById('generateDocBtn');

        if (docTemplateSelect) {
            docTemplateSelect.addEventListener('change', (e) => {
                this.loadDocumentForm(e.target.value);
            });
        }

        if (generateBtn) {
            generateBtn.addEventListener('click', (e) => {
                e.preventDefault();
                this.generateDocument();
            });
        }
    }

    handleFileSelection(file) {
        if (!file) return;

        // Validate file
        const supportedTypes = ['application/pdf', 'application/msword', 'application/vnd.openxmlformats-officedocument.wordprocessingml.document', 'text/plain'];
        const maxSize = 10 * 1024 * 1024; // 10MB

        if (!supportedTypes.includes(file.type)) {
            this.showMessage('Unsupported file format. Please use PDF, DOC, DOCX, or TXT files.', 'error');
            return;
        }

        if (file.size > maxSize) {
            this.showMessage('File size exceeds 10MB limit.', 'error');
            return;
        }

        // Show file preview
        this.showFilePreview(file);

        // Simulate file content reading for demo
        const sampleContent = `Sample Template Content

This is a sample template with placeholders:
Name: {{Name}}
Date: {{Date}}
Department: {{Department}}
Description: {{Description}}

Thank you,
{{Signature}}`;

        this.detectPlaceholders(sampleContent);
        this.pendingTemplate = {
            file: file,
            content: sampleContent
        };
    }

    showFilePreview(file) {
        const filePreview = document.getElementById('filePreview');
        const fileTypeIcon = this.getFileTypeIcon(file.type);
        
        if (filePreview) {
            filePreview.innerHTML = `
                <div class="file-info">
                    <div class="file-icon">${fileTypeIcon}</div>
                    <div class="file-details">
                        <h4>${file.name}</h4>
                        <p>Size: ${this.formatFileSize(file.size)} | Type: ${file.type}</p>
                    </div>
                </div>
            `;
            filePreview.classList.remove('hidden');
        }

        // Enable buttons
        const previewBtn = document.getElementById('previewTemplateBtn');
        const submitBtn = document.querySelector('#templateUploadForm button[type="submit"]');
        if (previewBtn) previewBtn.disabled = false;
        if (submitBtn) submitBtn.disabled = false;
    }

    detectPlaceholders(content) {
        const placeholderRegex = /\{\{([^}]+)\}\}/g;
        const placeholders = new Set();
        let match;

        while ((match = placeholderRegex.exec(content)) !== null) {
            placeholders.add(match[1].trim());
        }

        this.showDetectedPlaceholders(Array.from(placeholders));
    }

    showDetectedPlaceholders(placeholders) {
        const section = document.getElementById('placeholderSection');
        const container = document.getElementById('detectedPlaceholders');

        if (placeholders.length > 0) {
            container.innerHTML = placeholders.map(placeholder => 
                `<div class="placeholder-item">{{${placeholder}}}</div>`
            ).join('');
            section.classList.remove('hidden');
        } else {
            section.classList.add('hidden');
            this.showMessage('No placeholders detected in the template. Make sure to use {{placeholder}} format.', 'warning');
        }
    }

    previewTemplate() {
        if (!this.pendingTemplate) {
            this.showMessage('Please select a template file first.', 'error');
            return;
        }

        const previewContent = document.getElementById('templatePreviewContent');
        if (previewContent) {
            previewContent.textContent = this.pendingTemplate.content;
        }

        this.showModal('templatePreviewModal');
    }

    handleTemplateUpload() {
        if (!this.pendingTemplate) {
            this.showMessage('Please select a template file first.', 'error');
            return;
        }

        const formData = {
            title: document.getElementById('templateTitle').value,
            description: document.getElementById('templateDescription').value,
            category: document.getElementById('templateCategory').value,
            tags: document.getElementById('templateTags').value
        };

        // Validation
        if (!formData.title || !formData.category) {
            this.showMessage('Please fill in required fields (Title and Category).', 'error');
            return;
        }

        // Check permissions
        if (!this.hasTemplatePermission('create')) {
            this.showMessage('You do not have permission to create templates.', 'error');
            return;
        }

        // Create template
        const newTemplate = {
            id: Date.now(),
            name: formData.title,
            description: formData.description,
            category: formData.category,
            version: "1.0",
            status: "Published",
            created: new Date().toISOString(),
            lastModified: new Date().toISOString(),
            createdBy: this.currentUser.name,
            approvedBy: this.currentUser.name,
            usageCount: 0,
            rating: 4.5,
            tags: formData.tags.split(',').map(tag => tag.trim()).filter(tag => tag),
            visitorAccess: true,
            isPublic: true,
            fileFormats: ["pdf", "doc"],
            content: this.pendingTemplate.content,
            fields: this.generateFieldsFromPlaceholders(this.pendingTemplate.content)
        };

        this.data.templates.push(newTemplate);
        this.showMessage('Template uploaded and published successfully!', 'success');
        
        // Reset form
        document.getElementById('templateUploadForm').reset();
        document.getElementById('filePreview').classList.add('hidden');
        document.getElementById('placeholderSection').classList.add('hidden');
        document.getElementById('previewTemplateBtn').disabled = true;
        document.querySelector('#templateUploadForm button[type="submit"]').disabled = true;
        this.pendingTemplate = null;

        // Update template count if on templates page
        if (this.currentPage === 'templates') {
            this.loadTemplates();
        }
    }

    publishTemplate() {
        this.closeModal(document.getElementById('templatePreviewModal'));
        this.handleTemplateUpload();
    }

    generateFieldsFromPlaceholders(content) {
        const placeholderRegex = /\{\{([^}]+)\}\}/g;
        const placeholders = new Set();
        let match;

        while ((match = placeholderRegex.exec(content)) !== null) {
            placeholders.add(match[1].trim());
        }

        return Array.from(placeholders).map(placeholder => ({
            name: placeholder,
            type: this.guessFieldType(placeholder),
            label: this.formatPlaceholder(placeholder),
            required: true
        }));
    }

    guessFieldType(placeholder) {
        const lower = placeholder.toLowerCase();
        if (lower.includes('date')) return 'date';
        if (lower.includes('time')) return 'time';
        if (lower.includes('email')) return 'email';
        if (lower.includes('phone') || lower.includes('mobile')) return 'tel';
        if (lower.includes('number') || lower.includes('amount') || lower.includes('rate')) return 'number';
        if (lower.includes('description') || lower.includes('history') || lower.includes('conclusion')) return 'textarea';
        return 'text';
    }

    hasTemplatePermission(permission) {
        if (!this.currentUser) return false;
        return this.currentUser.templatePermissions.includes(permission);
    }

    showPage(pageId) {
        // Update navigation
        document.querySelectorAll('.nav-link').forEach(link => link.classList.remove('active'));
        const currentNavLink = document.querySelector(`[data-page="${pageId}"]`);
        if (currentNavLink) {
            currentNavLink.classList.add('active');
        }

        // Show page
        document.querySelectorAll('.content-page').forEach(page => page.classList.remove('active'));
        const currentPage = document.getElementById(`${pageId}Page`);
        if (currentPage) {
            currentPage.classList.add('active');
        }

        this.currentPage = pageId;
        this.loadPageContent(pageId);
    }

    loadPageContent(pageId) {
        switch (pageId) {
            case 'dashboard':
                this.loadDashboard();
                break;
            case 'templates':
                this.loadTemplates();
                break;
            case 'documents':
                this.loadDocuments();
                break;
            case 'library':
                this.loadLibrary();
                break;
            case 'users':
                this.loadUsers();
                break;
            case 'organizations':
                this.loadOrganizations();
                break;
            case 'reports':
                this.loadReports();
                break;
            case 'profile':
                this.loadProfile();
                break;
        }
    }

    loadDashboard() {
        // Update stats
        const stats = {
            totalTemplates: this.data.templates.length,
            totalDocuments: this.data.documents.length,
            activeUsers: this.data.users.filter(u => u.status === 'Active').length,
            templateCategories: this.data.templateCategories.length
        };

        Object.entries(stats).forEach(([key, value]) => {
            const element = document.getElementById(key);
            if (element) {
                element.textContent = typeof value === 'number' ? value.toLocaleString() : value;
            }
        });

        this.loadPopularTemplates();
        setTimeout(() => this.loadUsageChart(), 100);
    }

    loadPopularTemplates() {
        const container = document.getElementById('popularTemplates');
        if (!container) return;

        const popularTemplates = this.data.templates
            .sort((a, b) => b.usageCount - a.usageCount)
            .slice(0, 5);

        container.innerHTML = popularTemplates.map(template => `
            <div class="template-item">
                <div class="template-details">
                    <h4>${template.name}</h4>
                    <p>${template.category}</p>
                </div>
                <div class="usage-count">${template.usageCount}</div>
            </div>
        `).join('');
    }

    loadUsageChart() {
        const ctx = document.getElementById('usageChart');
        if (ctx && typeof Chart !== 'undefined') {
            const categoryUsage = this.data.templateCategories.map(cat => ({
                label: cat.name,
                value: cat.templates,
                color: cat.color
            }));

            new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: categoryUsage.map(item => item.label),
                    datasets: [{
                        data: categoryUsage.map(item => item.value),
                        backgroundColor: ['#1FB8CD', '#FFC185', '#B4413C', '#ECEBD5']
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            position: 'bottom'
                        }
                    }
                }
            });
        }
    }

    loadTemplates() {
        this.loadTemplateCategories();
        this.loadTemplatesList();
        this.bindTemplateFilters();
    }

    loadTemplateCategories() {
        const container = document.getElementById('templateCategories');
        if (!container) return;

        container.innerHTML = this.data.templateCategories.map(category => `
            <div class="category-card" onclick="app.filterByCategory('${category.name}')">
                <div class="category-header">
                    <div class="category-icon" style="background: ${category.color}20; color: ${category.color};">
                        ${category.icon}
                    </div>
                    <h3 class="category-title">${category.name}</h3>
                </div>
                <p class="category-description">${category.description}</p>
                <div class="category-stats">
                    <span>${category.templates} templates</span>
                    <span>${category.subcategories.length} subcategories</span>
                </div>
            </div>
        `).join('');
    }

    loadTemplatesList(filteredTemplates = null) {
        const container = document.getElementById('templatesList');
        if (!container) return;

        const templates = filteredTemplates || this.data.templates;

        if (templates.length === 0) {
            container.innerHTML = '<div class="empty-state"><h3>No templates found</h3><p>Try adjusting your search criteria.</p></div>';
            return;
        }

        container.innerHTML = templates.map(template => `
            <div class="template-card card">
                <div class="card__body">
                    <div class="template-header">
                        <div class="template-meta">
                            <h3>${template.name}</h3>
                            <div class="template-category">${template.category}</div>
                            <div class="template-rating">⭐ ${template.rating || 0} (${template.usageCount} uses)</div>
                        </div>
                        <div class="status status--success">${template.status}</div>
                    </div>
                    <p>${template.description}</p>
                    <div class="placeholders-preview">
                        <p><strong>Fields (${template.fields?.length || 0}):</strong></p>
                        <div class="placeholder-tags">
                            ${(template.fields || []).slice(0, 5).map(field => `<span class="placeholder-tag">{{${field.name}}}</span>`).join('')}
                            ${(template.fields?.length || 0) > 5 ? `<span class="placeholder-tag">+${template.fields.length - 5} more</span>` : ''}
                        </div>
                    </div>
                    <div class="template-actions">
                        <button class="btn btn--sm btn--primary" onclick="app.useTemplate(${template.id})">Use Template</button>
                        ${this.hasTemplatePermission('edit') ? `<button class="btn btn--sm btn--outline" onclick="app.editTemplate(${template.id})">Edit</button>` : ''}
                        ${this.hasTemplatePermission('delete') ? `<button class="btn btn--sm btn--outline" onclick="app.deleteTemplate(${template.id})">Delete</button>` : ''}
                    </div>
                </div>
            </div>
        `).join('');
    }

    bindTemplateFilters() {
        const searchInput = document.getElementById('templateSearch');
        const categoryFilter = document.getElementById('categoryFilter');

        if (searchInput) {
            searchInput.addEventListener('input', () => {
                this.filterTemplates();
            });
        }

        if (categoryFilter) {
            // Populate category filter
            categoryFilter.innerHTML = '<option value="">All Categories</option>' + 
                this.data.templateCategories.map(cat => 
                    `<option value="${cat.name}">${cat.name}</option>`
                ).join('');

            categoryFilter.addEventListener('change', () => {
                this.filterTemplates();
            });
        }
    }

    filterTemplates() {
        const searchTerm = document.getElementById('templateSearch')?.value.toLowerCase() || '';
        const selectedCategory = document.getElementById('categoryFilter')?.value || '';

        let filtered = this.data.templates;

        if (searchTerm) {
            filtered = filtered.filter(template => 
                template.name.toLowerCase().includes(searchTerm) ||
                template.description.toLowerCase().includes(searchTerm) ||
                template.tags.some(tag => tag.toLowerCase().includes(searchTerm))
            );
        }

        if (selectedCategory) {
            filtered = filtered.filter(template => template.category === selectedCategory);
        }

        this.loadTemplatesList(filtered);
    }

    filterByCategory(categoryName) {
        const categoryFilter = document.getElementById('categoryFilter');
        if (categoryFilter) {
            categoryFilter.value = categoryName;
            this.filterTemplates();
        }
    }

    loadDocuments() {
        const select = document.getElementById('docTemplateSelect');
        if (select) {
            const accessibleTemplates = this.getAccessibleTemplates();
            select.innerHTML = '<option value="">Select a template...</option>' + 
                accessibleTemplates.map(template => 
                    `<option value="${template.id}">${template.name}</option>`
                ).join('');
        }
    }

    getAccessibleTemplates() {
        if (this.currentUser.role === 'visitor') {
            return this.data.templates.filter(t => t.visitorAccess);
        }
        return this.data.templates;
    }

    loadDocumentForm(templateId) {
        const template = this.data.templates.find(t => t.id == templateId);
        const formContainer = document.getElementById('documentForm');
        const generateActions = document.getElementById('generateActions');
        
        if (!template || !formContainer) {
            if (formContainer) formContainer.innerHTML = '';
            if (generateActions) generateActions.classList.add('hidden');
            return;
        }

        // Check visitor limits
        if (this.currentUser.role === 'visitor' && this.visitorDocumentCount >= this.maxVisitorDocuments) {
            this.showMessage('You have reached the document generation limit for visitors. Please create an account for unlimited access.', 'warning');
            return;
        }

        const formHTML = template.fields.map(field => {
            const inputType = field.type || 'text';
            let inputHTML;

            if (inputType === 'select' && field.options) {
                inputHTML = `
                    <select id="doc_${field.name}" name="${field.name}" class="form-control" ${field.required ? 'required' : ''}>
                        <option value="">Select ${field.label}</option>
                        ${field.options.map(option => `<option value="${option}">${option}</option>`).join('')}
                    </select>
                `;
            } else if (inputType === 'textarea') {
                inputHTML = `<textarea id="doc_${field.name}" name="${field.name}" class="form-control" rows="3" placeholder="Enter ${field.label}" ${field.required ? 'required' : ''}></textarea>`;
            } else {
                inputHTML = `<input type="${inputType}" id="doc_${field.name}" name="${field.name}" class="form-control" placeholder="Enter ${field.label}" ${field.required ? 'required' : ''}>`;
            }

            return `
                <div class="form-group">
                    <label for="doc_${field.name}" class="form-label">
                        ${field.label}${field.required ? ' *' : ''}
                    </label>
                    ${inputHTML}
                </div>
            `;
        }).join('');

        formContainer.innerHTML = formHTML;
        
        if (generateActions) {
            generateActions.classList.remove('hidden');
            generateActions.setAttribute('data-template-id', templateId);
        }

        // Update preview as user types
        const inputs = formContainer.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.addEventListener('input', () => this.updateDocumentPreview(templateId));
        });

        this.updateDocumentPreview(templateId);
    }

    updateDocumentPreview(templateId) {
        const template = this.data.templates.find(t => t.id == templateId);
        const previewContainer = document.getElementById('documentPreview');
        
        if (!template || !previewContainer) return;

        // Get form data
        const formData = {};
        document.querySelectorAll('#documentForm input, #documentForm select, #documentForm textarea').forEach(input => {
            formData[input.name] = input.value || `[${input.name}]`;
        });

        if (template.useTableLayout && template.id === 2) {
            previewContainer.innerHTML = this.generateTAFormPreview(formData);
        } else {
            let content = template.content;
            Object.entries(formData).forEach(([key, value]) => {
                const placeholder = `{{${key}}}`;
                content = content.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value);
            });

            previewContainer.innerHTML = `
                <div class="document-content">
                    <pre>${content}</pre>
                </div>
            `;
        }
    }

    generateTAFormPreview(formData) {
        return `
            <div class="document-content">
                <div class="ta-form-header">
                    <strong>CENTRAL RAILWAY</strong><br><br>
                    <strong>TRAVELING ALLOWANCE JOURNAL</strong><br><br>
                    <strong>GA 31 SRC/G 1677</strong>
                </div>
                
                <div class="ta-form-details">
                    <strong>Rule by which governed:</strong> 7th CPC<br><br>
                    <strong>Branch:</strong> ${formData.Branch || '[Branch]'} &nbsp;&nbsp;
                    <strong>Division:</strong> ${formData.Division || '[Division]'} &nbsp;&nbsp;
                    <strong>Headquarter:</strong> ${formData.Headquarter || '[Headquarter]'}<br><br>
                    <strong>Employee:</strong> ${formData.NameofEmployee || '[NameofEmployee]'} 
                    <strong>Month:</strong> ${formData.Month || '[Month]'}<br><br>
                    <strong>Designation:</strong> ${formData.Designation || '[Designation]'} &nbsp;&nbsp;
                    <strong>Basic:</strong> ${formData.Basic || '[Basic]'} &nbsp;&nbsp;
                    <strong>Grade Pay:</strong> ${formData.GradePay || '[GradePay]'}<br>
                    <strong>PF No:</strong> ${formData.PFNo || '[PFNo]'} &nbsp;&nbsp;
                    <strong>Mobile:</strong> ${formData.MobileNo || '[MobileNo]'}
                </div>

                <table class="government-table">
                    <thead>
                        <tr>
                            <th>Date</th><th>Train</th><th>From</th><th>To</th><th>Kms</th><th>Day/Night</th><th>Purpose</th><th>Rate</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr>
                            <td>${formData.MonthandDate1 || '[Date]'}</td>
                            <td>${formData.TrainNo1 || '[Train]'}</td>
                            <td>${formData.StationFrom1 || '[From]'}</td>
                            <td>${formData.StationTo1 || '[To]'}</td>
                            <td class="center">${formData.Kms1 || '[Kms]'}</td>
                            <td class="center">${formData.DayNight1 || '[Day/Night]'}</td>
                            <td>${formData.ObjectofJourney1 || '[Purpose]'}</td>
                            <td class="amount">${formData.Rate1 || '[Rate]'}</td>
                        </tr>
                    </tbody>
                </table>

                <div style="text-align: right; margin-top: 20px;">
                    <strong>Total Amount:</strong> ${formData.Total || '[Total]'}<br>
                    <strong>In Words:</strong> ${formData.TotalWords || '[TotalWords]'}<br><br>
                    <strong>Signature:</strong> ${formData.Sign || '[Signature]'}
                </div>
            </div>
        `;
    }

    generateDocument() {
        const templateId = document.getElementById('generateActions').getAttribute('data-template-id');
        const template = this.data.templates.find(t => t.id == templateId);
        
        if (!template) return;

        // Check visitor limits
        if (this.currentUser.role === 'visitor') {
            if (this.visitorDocumentCount >= this.maxVisitorDocuments) {
                this.showMessage('Document generation limit reached. Please create an account for unlimited access.', 'error');
                return;
            }
            this.visitorDocumentCount++;
            this.updateVisitorLimits();
        }

        const formData = {};
        document.querySelectorAll('#documentForm input, #documentForm select, #documentForm textarea').forEach(input => {
            formData[input.name] = input.value;
        });

        // Validate required fields
        const missingFields = template.fields.filter(field => 
            field.required && (!formData[field.name] || formData[field.name].trim() === '')
        );

        if (missingFields.length > 0) {
            this.showMessage(`Please fill in all required fields: ${missingFields.map(f => f.label).join(', ')}`, 'error');
            return;
        }

        // Generate document
        const newDoc = {
            id: Date.now(),
            template: template.name,
            generatedBy: this.currentUser.name,
            date: new Date().toISOString().split('T')[0],
            status: 'Completed',
            category: template.category,
            data: formData
        };

        this.data.documents.unshift(newDoc);

        // Download documents
        const generatePDF = document.getElementById('generatePDF').checked;
        const generateDOC = document.getElementById('generateDOC').checked;

        if (generatePDF) {
            this.downloadDocument(template, formData, 'pdf');
        }
        if (generateDOC) {
            this.downloadDocument(template, formData, 'doc');
        }

        this.showMessage('Document generated and downloaded successfully!', 'success');
        
        // Update template usage count
        template.usageCount = (template.usageCount || 0) + 1;
    }

    downloadDocument(template, data, format) {
        let content;
        let filename;
        let mimeType;

        if (template.useTableLayout && template.id === 2) {
            content = this.generateTAFormHTML(data);
            filename = `${template.name}_${Date.now()}.html`;
            mimeType = 'text/html';
        } else {
            content = template.content;
            Object.entries(data).forEach(([key, value]) => {
                const placeholder = `{{${key}}}`;
                content = content.replace(new RegExp(placeholder.replace(/[.*+?^${}()|[\]\\]/g, '\\$&'), 'g'), value || `[${key}]`);
            });
            
            if (format === 'pdf') {
                filename = `${template.name}_${Date.now()}.html`;
                mimeType = 'text/html';
                content = `<!DOCTYPE html><html><head><title>${template.name}</title><style>body{font-family:Arial,sans-serif;padding:20px;}</style></head><body><pre>${content}</pre></body></html>`;
            } else {
                filename = `${template.name}_${Date.now()}.txt`;
                mimeType = 'text/plain';
            }
        }
        
        const blob = new Blob([content], { type: mimeType });
        const url = URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        URL.revokeObjectURL(url);
    }

    generateTAFormHTML(data) {
        return `<!DOCTYPE html>
<html><head><title>Travel Allowance Form</title>
<style>
body{font-family:Arial,sans-serif;padding:20px;}
.government-table{width:100%;border-collapse:collapse;border:1px solid black;margin:16px 0;}
.government-table th,.government-table td{border:1px solid black;padding:8px;text-align:left;}
.government-table th{background:#f5f5f5;font-weight:bold;text-align:center;}
.center{text-align:center;}.amount{text-align:right;}
.ta-form-header{text-align:center;margin-bottom:24px;font-weight:bold;}
</style></head><body>${this.generateTAFormPreview(data)}</body></html>`;
    }

    loadLibrary() {
        const container = document.getElementById('documentsTable');
        if (!container) return;
        
        const userDocuments = this.currentUser.role === 'visitor' ? 
            [] : this.data.documents.filter(doc => 
                this.currentUser.role === 'super_admin' || 
                this.currentUser.role === 'admin' || 
                doc.generatedBy === this.currentUser.name
            );

        if (userDocuments.length === 0) {
            container.innerHTML = '<div class="empty-state"><h3>No documents found</h3><p>Generate some documents to see them here.</p></div>';
            return;
        }

        const tableHTML = `
            <table class="table">
                <thead>
                    <tr><th>Template</th><th>Generated By</th><th>Date</th><th>Category</th><th>Status</th><th>Actions</th></tr>
                </thead>
                <tbody>
                    ${userDocuments.map(doc => `
                        <tr>
                            <td>${doc.template}</td>
                            <td>${doc.generatedBy}</td>
                            <td>${doc.date}</td>
                            <td>${doc.category}</td>
                            <td><div class="status status--${this.getStatusClass(doc.status)}">${doc.status}</div></td>
                            <td><div class="table-actions">
                                <button class="btn btn--sm btn--outline" onclick="app.viewDocument(${doc.id})">View</button>
                                <button class="btn btn--sm btn--outline" onclick="app.downloadDocument(${doc.id})">Download</button>
                            </div></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    loadUsers() {
        if (!this.hasPermission('user_manage')) return;

        const container = document.getElementById('usersTable');
        if (!container) return;

        const tableHTML = `
            <table class="table">
                <thead>
                    <tr><th>Name</th><th>Email</th><th>Role</th><th>Organization</th><th>Status</th><th>Actions</th></tr>
                </thead>
                <tbody>
                    ${this.data.users.map(user => `
                        <tr>
                            <td>${user.name}</td>
                            <td>${user.email}</td>
                            <td><span class="user-role ${user.role}">${user.role.replace('_', ' ')}</span></td>
                            <td>${user.organization}</td>
                            <td><div class="status status--success">${user.status}</div></td>
                            <td><div class="table-actions">
                                <button class="btn btn--sm btn--outline">Edit</button>
                                <button class="btn btn--sm btn--outline">Deactivate</button>
                            </div></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    loadOrganizations() {
        if (!this.hasPermission('organization_settings')) return;

        const container = document.getElementById('organizationsTable');
        if (!container) return;

        const tableHTML = `
            <table class="table">
                <thead>
                    <tr><th>Name</th><th>Code</th><th>Users</th><th>Templates</th><th>Documents</th><th>Status</th><th>Actions</th></tr>
                </thead>
                <tbody>
                    ${this.data.organizations.map(org => `
                        <tr>
                            <td>${org.name}</td>
                            <td>${org.code}</td>
                            <td>${org.activeUsers}/${org.totalUsers}</td>
                            <td>${org.totalTemplates}</td>
                            <td>${org.totalDocuments.toLocaleString()}</td>
                            <td><div class="status status--success">${org.status}</div></td>
                            <td><div class="table-actions">
                                <button class="btn btn--sm btn--outline">Edit</button>
                                <button class="btn btn--sm btn--outline">Settings</button>
                            </div></td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
        `;

        container.innerHTML = tableHTML;
    }

    loadReports() {
        this.loadTrendsChart();
        this.loadCategoryStats();
    }

    loadTrendsChart() {
        const ctx = document.getElementById('trendsChart');
        if (ctx && typeof Chart !== 'undefined') {
            new Chart(ctx, {
                type: 'line',
                data: {
                    labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                    datasets: [{
                        label: 'Documents Generated',
                        data: [65, 78, 90, 81, 96, 105],
                        borderColor: '#1FB8CD',
                        backgroundColor: 'rgba(31, 184, 205, 0.1)',
                        tension: 0.4
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false
                }
            });
        }
    }

    loadCategoryStats() {
        const container = document.getElementById('categoryStats');
        if (!container) return;

        container.innerHTML = this.data.templateCategories.map(category => {
            const percentage = (category.templates / this.data.templates.length) * 100;
            return `
                <div class="stat-item">
                    <div>
                        <strong>${category.name}</strong>
                        <div>${category.templates} templates</div>
                    </div>
                    <div class="stat-bar">
                        <div class="stat-bar-fill" style="width: ${percentage}%"></div>
                    </div>
                </div>
            `;
        }).join('');
    }

    loadProfile() {
        if (this.currentUser.role === 'visitor') return;

        const profileName = document.getElementById('profileName');
        const profileEmail = document.getElementById('profileEmail');
        const profileOrg = document.getElementById('profileOrg');
        const profileRole = document.getElementById('profileRole');

        if (profileName) profileName.value = this.currentUser.name;
        if (profileEmail) profileEmail.value = this.currentUser.email;
        if (profileOrg) profileOrg.value = this.currentUser.organization;
        if (profileRole) profileRole.value = this.currentUser.role.replace('_', ' ');

        const profileForm = document.getElementById('profileForm');
        if (profileForm) {
            profileForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.updateProfile();
            });
        }
    }

    updateProfile() {
        const name = document.getElementById('profileName').value;
        const email = document.getElementById('profileEmail').value;

        if (name && email) {
            this.currentUser.name = name;
            this.currentUser.email = email;
            this.updateUserInterface();
            this.showMessage('Profile updated successfully!', 'success');
        }
    }

    // Template actions
    useTemplate(templateId) {
        this.showPage('documents');
        setTimeout(() => {
            const select = document.getElementById('docTemplateSelect');
            if (select) {
                select.value = templateId;
                this.loadDocumentForm(templateId);
            }
        }, 100);
    }

    editTemplate(templateId) {
        this.showMessage('Template editing functionality would be available in the full version.', 'info');
    }

    deleteTemplate(templateId) {
        if (!this.hasTemplatePermission('delete')) {
            this.showMessage('You do not have permission to delete templates.', 'error');
            return;
        }

        if (confirm('Are you sure you want to delete this template?')) {
            this.data.templates = this.data.templates.filter(t => t.id !== templateId);
            this.loadTemplates();
            this.showMessage('Template deleted successfully!', 'success');
        }
    }

    viewDocument(docId) {
        this.showMessage('Document viewer would be implemented in the full version.', 'info');
    }

    // Utility functions
    getStatusClass(status) {
        switch (status.toLowerCase()) {
            case 'completed': return 'success';
            case 'pending': return 'warning';
            case 'approved': return 'success';
            default: return 'info';
        }
    }

    getFileTypeIcon(mimeType) {
        if (mimeType.includes('pdf')) return '📄';
        if (mimeType.includes('word')) return '📝';
        if (mimeType.includes('text')) return '📋';
        return '📎';
    }

    formatFileSize(bytes) {
        if (bytes === 0) return '0 Bytes';
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    formatPlaceholder(placeholder) {
        return placeholder.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase()).trim();
    }

    showModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    closeModal(modal) {
        if (modal) {
            modal.classList.add('hidden');
        }
    }

    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `message message--${type}`;
        messageDiv.textContent = message;
        messageDiv.style.position = 'fixed';
        messageDiv.style.top = '20px';
        messageDiv.style.right = '20px';
        messageDiv.style.zIndex = '10000';
        messageDiv.style.maxWidth = '400px';
        
        document.body.appendChild(messageDiv);
        
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 4000);
    }

    logout() {
        this.currentUser = null;
        this.visitorDocumentCount = 0;
        this.pendingTemplate = null;
        this.showLoginPage();
        this.showMessage('Logged out successfully', 'success');
    }
}

// Initialize application
new DocuFillApp();