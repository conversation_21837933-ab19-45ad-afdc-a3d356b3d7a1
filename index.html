<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DocuFill - Document Template Management System</title>
    <link rel="stylesheet" href="style.css">
</head>
<body>
    <!-- Login Page -->
    <div id="loginPage" class="page active">
        <div class="login-container">
            <div class="login-card card">
                <div class="card__body">
                    <h1 class="login-title">DocuFill</h1>
                    <p class="login-subtitle">Document Template Management System</p>
                    
                    <form id="loginForm" class="login-form">
                        <div class="form-group">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" id="email" class="form-control" placeholder="Enter your email" required>
                        </div>
                        <div class="form-group">
                            <label for="password" class="form-label">Password</label>
                            <input type="password" id="password" class="form-control" placeholder="Enter your password" required>
                        </div>
                        <button type="submit" class="btn btn--primary btn--full-width">Sign In</button>
                    </form>
                    
                    <div class="visitor-access">
                        <button class="btn btn--outline btn--full-width" id="visitorAccessBtn">Continue as Visitor</button>
                        <p class="visitor-info">Limited to 5 documents per session</p>
                    </div>
                    
                    <div class="auth-links">
                        <a href="#" id="createAccountLink" class="auth-link">Create Account</a>
                        <a href="#" id="forgotPasswordLink" class="auth-link">Forgot Password?</a>
                    </div>
                    
                    <div class="demo-accounts">
                        <p class="demo-title">Demo Accounts:</p>
                        <div class="demo-list">
                            <button class="btn btn--sm btn--outline demo-btn super-admin-btn" data-email="<EMAIL>" data-role="super_admin">Super Admin</button>
                            <button class="btn btn--sm btn--outline demo-btn admin-btn" data-email="<EMAIL>" data-role="admin">Admin</button>
                            <button class="btn btn--sm btn--outline demo-btn supervisor-btn" data-email="<EMAIL>" data-role="supervisor">Supervisor</button>
                            <button class="btn btn--sm btn--outline demo-btn employee-btn" data-email="<EMAIL>" data-role="employee">Employee</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Main Application -->
    <div id="mainApp" class="page">
        <!-- Sidebar Navigation -->
        <nav class="sidebar">
            <div class="sidebar-header">
                <h2 class="sidebar-title">DocuFill</h2>
                <div class="user-info">
                    <span id="userRole" class="user-role"></span>
                    <span id="userName" class="user-name"></span>
                    <div id="visitorLimits" class="visitor-limits hidden">
                        <span class="limit-text">Documents left: <span id="documentsLeft">5</span></span>
                    </div>
                </div>
            </div>
            
            <ul class="sidebar-nav">
                <li><a href="#" class="nav-link active" data-page="dashboard">Dashboard</a></li>
                <li><a href="#" class="nav-link" data-page="templates">Template Library</a></li>
                <li><a href="#" class="nav-link" data-page="upload" data-min-role="employee">Upload Template</a></li>
                <li><a href="#" class="nav-link" data-page="documents">Generate Documents</a></li>
                <li><a href="#" class="nav-link" data-page="library" data-min-role="employee">Document Library</a></li>
                <li class="admin-only"><a href="#" class="nav-link" data-page="users">User Management</a></li>
                <li class="admin-only"><a href="#" class="nav-link" data-page="organizations">Organizations</a></li>
                <li class="super-admin-only"><a href="#" class="nav-link" data-page="system">System Settings</a></li>
                <li><a href="#" class="nav-link" data-page="reports" data-min-role="supervisor">Reports & Analytics</a></li>
                <li><a href="#" class="nav-link" data-page="profile" data-min-role="employee">Profile</a></li>
                <li><a href="#" class="nav-link logout-link">Logout</a></li>
            </ul>
        </nav>

        <!-- Main Content Area -->
        <main class="main-content">
            <!-- Dashboard -->
            <div id="dashboardPage" class="content-page active">
                <div class="page-header">
                    <h1>Dashboard</h1>
                    <p>Overview of your document management activities</p>
                </div>
                
                <div class="stats-grid">
                    <div class="stat-card card">
                        <div class="card__body">
                            <h3>Available Templates</h3>
                            <div class="stat-value" id="totalTemplates">45</div>
                        </div>
                    </div>
                    <div class="stat-card card">
                        <div class="card__body">
                            <h3>Documents Generated</h3>
                            <div class="stat-value" id="totalDocuments">15,680</div>
                        </div>
                    </div>
                    <div class="stat-card card">
                        <div class="card__body">
                            <h3>Active Users</h3>
                            <div class="stat-value" id="activeUsers">1,180</div>
                        </div>
                    </div>
                    <div class="stat-card card">
                        <div class="card__body">
                            <h3>Template Categories</h3>
                            <div class="stat-value" id="templateCategories">4</div>
                        </div>
                    </div>
                </div>

                <div class="dashboard-content">
                    <div class="recent-activity card">
                        <div class="card__header">
                            <h2>Popular Templates</h2>
                        </div>
                        <div class="card__body">
                            <div id="popularTemplates" class="template-list"></div>
                        </div>
                    </div>
                    
                    <div class="template-usage card">
                        <div class="card__header">
                            <h2>Template Usage Analytics</h2>
                        </div>
                        <div class="card__body">
                            <div id="templateUsageChart" class="chart-container" style="height: 300px; position: relative;">
                                <canvas id="usageChart"></canvas>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Template Library -->
            <div id="templatesPage" class="content-page">
                <div class="page-header">
                    <h1>Template Library</h1>
                    <div class="page-actions">
                        <input type="text" id="templateSearch" class="form-control" placeholder="Search templates..." style="width: 300px;">
                        <select id="categoryFilter" class="form-control" style="width: 200px;">
                            <option value="">All Categories</option>
                        </select>
                    </div>
                </div>
                
                <div id="templateCategories" class="categories-grid"></div>
                
                <div id="templatesList" class="templates-grid"></div>
            </div>

            <!-- Upload Template -->
            <div id="uploadPage" class="content-page">
                <div class="page-header">
                    <h1>Upload New Template</h1>
                    <p>Upload document templates with automatic placeholder detection</p>
                </div>
                
                <div class="upload-container">
                    <div class="upload-form card">
                        <div class="card__body">
                            <form id="templateUploadForm">
                                <div class="form-group">
                                    <label for="templateTitle" class="form-label">Template Title</label>
                                    <input type="text" id="templateTitle" class="form-control" placeholder="Enter template title" required>
                                </div>
                                
                                <div class="form-group">
                                    <label for="templateDescription" class="form-label">Description</label>
                                    <textarea id="templateDescription" class="form-control" rows="3" placeholder="Describe what this template is used for"></textarea>
                                </div>
                                
                                <div class="form-group">
                                    <label for="templateCategory" class="form-label">Category</label>
                                    <select id="templateCategory" class="form-control" required>
                                        <option value="">Select category...</option>
                                        <option value="Railway Operations">Railway Operations</option>
                                        <option value="Financial & HR">Financial & HR</option>
                                        <option value="Compliance & Legal">Compliance & Legal</option>
                                        <option value="Administrative">Administrative</option>
                                    </select>
                                </div>
                                
                                <div class="form-group">
                                    <label for="templateTags" class="form-label">Tags (comma separated)</label>
                                    <input type="text" id="templateTags" class="form-control" placeholder="e.g. railway, report, maintenance">
                                </div>
                                
                                <div class="form-group">
                                    <label class="form-label">Template File</label>
                                    <div class="file-drop-zone" id="fileDropZone">
                                        <input type="file" id="templateFile" class="file-input" accept=".pdf,.doc,.docx,.txt" required>
                                        <div class="drop-content">
                                            <div class="drop-icon">📄</div>
                                            <p class="drop-title">Drag and drop your template file here</p>
                                            <p class="drop-subtitle">or click to browse files</p>
                                            <button type="button" class="btn btn--outline" id="browseFileBtn">Browse Files</button>
                                            <div class="supported-formats">
                                                <small>Supported formats: PDF, DOC, DOCX, TXT (Max: 10MB)</small>
                                            </div>
                                        </div>
                                    </div>
                                    <div id="filePreview" class="file-preview hidden"></div>
                                </div>
                                
                                <div id="placeholderSection" class="placeholder-section hidden">
                                    <h3>Detected Placeholders</h3>
                                    <p class="placeholder-help">These placeholders were automatically detected in your template:</p>
                                    <div id="detectedPlaceholders" class="placeholder-list"></div>
                                </div>
                                
                                <div class="form-actions">
                                    <button type="button" class="btn btn--outline" id="previewTemplateBtn" disabled>Preview Template</button>
                                    <button type="submit" class="btn btn--primary" disabled>Upload & Publish Template</button>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Generator -->
            <div id="documentsPage" class="content-page">
                <div class="page-header">
                    <h1>Generate Documents</h1>
                    <p>Fill templates and generate professional documents</p>
                </div>
                
                <div class="generator-layout">
                    <div class="form-section card">
                        <div class="card__header">
                            <h2>Select Template & Fill Data</h2>
                        </div>
                        <div class="card__body">
                            <div class="template-selector">
                                <label for="docTemplateSelect" class="form-label">Choose Template</label>
                                <select id="docTemplateSelect" class="form-control">
                                    <option value="">Select a template...</option>
                                </select>
                            </div>
                            <div id="documentForm" class="document-form"></div>
                            <div id="generateActions" class="generate-actions hidden">
                                <div class="output-format">
                                    <label class="form-label">Output Format:</label>
                                    <div class="format-options">
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="generatePDF" checked> PDF
                                        </label>
                                        <label class="checkbox-label">
                                            <input type="checkbox" id="generateDOC" checked> DOC
                                        </label>
                                    </div>
                                </div>
                                <button id="generateDocBtn" class="btn btn--primary btn--full-width">Generate & Download Document</button>
                            </div>
                        </div>
                    </div>
                    
                    <div class="preview-section card">
                        <div class="card__header">
                            <h2>Document Preview</h2>
                        </div>
                        <div class="card__body">
                            <div id="documentPreview" class="document-preview">
                                <p class="preview-placeholder">Select a template and fill the form to see preview</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Document Library -->
            <div id="libraryPage" class="content-page">
                <div class="page-header">
                    <h1>Document Library</h1>
                    <div class="page-actions">
                        <input type="text" id="librarySearch" class="form-control" placeholder="Search documents..." style="width: 300px;">
                        <button class="btn btn--outline" id="exportDocsBtn">Export All</button>
                    </div>
                </div>
                
                <div class="library-filters">
                    <select id="statusFilter" class="form-control">
                        <option value="">All Status</option>
                        <option value="Completed">Completed</option>
                        <option value="Pending">Pending</option>
                        <option value="Approved">Approved</option>
                    </select>
                    
                    <select id="typeFilter" class="form-control">
                        <option value="">All Types</option>
                        <option value="Railway Operations">Railway Operations</option>
                        <option value="Financial & HR">Financial & HR</option>
                        <option value="Compliance & Legal">Compliance & Legal</option>
                        <option value="Administrative">Administrative</option>
                    </select>
                </div>
                
                <div id="documentsTable" class="documents-table"></div>
            </div>

            <!-- User Management (Admin Only) -->
            <div id="usersPage" class="content-page admin-only">
                <div class="page-header">
                    <h1>User Management</h1>
                    <button class="btn btn--primary" id="addUserBtn">Add New User</button>
                </div>
                
                <div id="usersTable" class="users-table"></div>
            </div>

            <!-- Organizations (Admin Only) -->
            <div id="organizationsPage" class="content-page admin-only">
                <div class="page-header">
                    <h1>Organizations</h1>
                    <button class="btn btn--primary" id="addOrgBtn">Add Organization</button>
                </div>
                
                <div id="organizationsTable" class="organizations-table"></div>
            </div>

            <!-- System Settings (Super Admin Only) -->
            <div id="systemPage" class="content-page super-admin-only">
                <div class="page-header">
                    <h1>System Administration</h1>
                    <p>Platform-wide settings and configuration</p>
                </div>
                
                <div class="system-settings">
                    <div class="setting-section card">
                        <div class="card__header">
                            <h2>Upload Settings</h2>
                        </div>
                        <div class="card__body">
                            <div class="form-group">
                                <label class="form-label">Maximum File Size</label>
                                <input type="text" class="form-control" value="10MB" readonly>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Supported Formats</label>
                                <input type="text" class="form-control" value="PDF, DOC, DOCX, TXT" readonly>
                            </div>
                        </div>
                    </div>
                    
                    <div class="setting-section card">
                        <div class="card__header">
                            <h2>User Limits</h2>
                        </div>
                        <div class="card__body">
                            <div class="form-group">
                                <label class="form-label">Visitor Document Limit</label>
                                <input type="number" class="form-control" value="5">
                            </div>
                            <div class="form-group">
                                <label class="form-label">Session Duration (hours)</label>
                                <input type="number" class="form-control" value="24">
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Reports & Analytics -->
            <div id="reportsPage" class="content-page">
                <div class="page-header">
                    <h1>Reports & Analytics</h1>
                </div>
                
                <div class="reports-grid">
                    <div class="report-card card">
                        <div class="card__header">
                            <h2>Document Generation Trends</h2>
                        </div>
                        <div class="card__body">
                            <div class="chart-container" style="height: 250px; position: relative;">
                                <canvas id="trendsChart"></canvas>
                            </div>
                        </div>
                    </div>
                    
                    <div class="report-card card">
                        <div class="card__header">
                            <h2>Template Categories</h2>
                        </div>
                        <div class="card__body">
                            <div id="categoryStats" class="category-stats"></div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Profile -->
            <div id="profilePage" class="content-page">
                <div class="page-header">
                    <h1>Profile Settings</h1>
                </div>
                
                <div class="profile-card card">
                    <div class="card__body">
                        <form id="profileForm">
                            <div class="form-group">
                                <label for="profileName" class="form-label">Full Name</label>
                                <input type="text" id="profileName" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="profileEmail" class="form-label">Email</label>
                                <input type="email" id="profileEmail" class="form-control">
                            </div>
                            <div class="form-group">
                                <label for="profileOrg" class="form-label">Organization</label>
                                <input type="text" id="profileOrg" class="form-control" readonly>
                            </div>
                            <div class="form-group">
                                <label for="profileRole" class="form-label">Role</label>
                                <input type="text" id="profileRole" class="form-control" readonly>
                            </div>
                            <button type="submit" class="btn btn--primary">Update Profile</button>
                        </form>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- Template Preview Modal -->
    <div id="templatePreviewModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Template Preview</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <div id="templatePreviewContent" class="template-preview-content"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--outline modal-close">Close</button>
                <button type="button" class="btn btn--primary" id="publishTemplateBtn">Publish Template</button>
            </div>
        </div>
    </div>

    <!-- Create Account Modal -->
    <div id="createAccountModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Create Account</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <form id="createAccountForm">
                    <div class="form-group">
                        <label for="regName" class="form-label">Full Name</label>
                        <input type="text" id="regName" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="regEmail" class="form-label">Email</label>
                        <input type="email" id="regEmail" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="regPassword" class="form-label">Password</label>
                        <input type="password" id="regPassword" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="regConfirmPassword" class="form-label">Confirm Password</label>
                        <input type="password" id="regConfirmPassword" class="form-control" required>
                    </div>
                    <div class="form-group">
                        <label for="regOrganization" class="form-label">Organization</label>
                        <select id="regOrganization" class="form-control" required>
                            <option value="">Select organization...</option>
                            <option value="Central Railway">Central Railway</option>
                            <option value="Western Railway">Western Railway</option>
                            <option value="Eastern Railway">Eastern Railway</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="regRole" class="form-label">Role</label>
                        <select id="regRole" class="form-control" required>
                            <option value="">Select role...</option>
                            <option value="employee">Employee</option>
                            <option value="supervisor">Supervisor</option>
                        </select>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--outline modal-close">Cancel</button>
                <button type="submit" form="createAccountForm" class="btn btn--primary">Create Account</button>
            </div>
        </div>
    </div>

    <!-- Forgot Password Modal -->
    <div id="forgotPasswordModal" class="modal hidden">
        <div class="modal-content">
            <div class="modal-header">
                <h2>Reset Password</h2>
                <button class="modal-close">&times;</button>
            </div>
            <div class="modal-body">
                <p>Enter your email address and we'll send you a link to reset your password.</p>
                <form id="forgotPasswordForm">
                    <div class="form-group">
                        <label for="resetEmail" class="form-label">Email</label>
                        <input type="email" id="resetEmail" class="form-control" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn--outline modal-close">Cancel</button>
                <button type="submit" form="forgotPasswordForm" class="btn btn--primary">Send Reset Link</button>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="app.js"></script>
</body>
</html>