import { useState, useEffect, useMemo, useCallback } from 'react'
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'
import { motion, AnimatePresence } from 'framer-motion'
import { 
  Search, 
  Filter, 
  Download, 
  Eye, 
  Trash2, 
  MoreHorizontal,
  Calendar,
  User,
  Mail,
  Phone,
  MapPin,
  Clock,
  CheckCircle,
  XCircle,
  AlertCircle,
  RefreshCw
} from 'lucide-react'
import { FormSubmission, FormSchema } from '@/types/form'
import { Input } from '@/components/ui/Input'
import { Button } from '@/components/ui/Button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Badge } from '@/components/ui/Badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/Table'
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/DropdownMenu'
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/Dialog'
import { DateRangePicker } from '@/components/ui/DateRangePicker'
import { Checkbox } from '@/components/ui/Checkbox'
import { SubmissionDetailView } from './SubmissionDetailView'
import { SubmissionExporter } from './SubmissionExporter'
import { SubmissionFilters } from './SubmissionFilters'
import { useSubmissions } from '@/hooks/useSubmissions'
import { useAuthStore } from '@/stores/authStore'
import { toast } from 'react-hot-toast'
import { format } from 'date-fns'

interface SubmissionManagerProps {
  formId: string
  schema: FormSchema
}

interface SubmissionFilters {
  search: string
  status: string
  dateRange: { from: Date | null; to: Date | null }
  paymentStatus: string
  sortBy: string
  sortOrder: 'asc' | 'desc'
}

export function SubmissionManager({ formId, schema }: SubmissionManagerProps) {
  const { hasPermission } = useAuthStore()
  const queryClient = useQueryClient()
  
  const [filters, setFilters] = useState<SubmissionFilters>({
    search: '',
    status: 'all',
    dateRange: { from: null, to: null },
    paymentStatus: 'all',
    sortBy: 'created_at',
    sortOrder: 'desc'
  })
  
  const [selectedSubmissions, setSelectedSubmissions] = useState<string[]>([])
  const [selectedSubmission, setSelectedSubmission] = useState<FormSubmission | null>(null)
  const [showDetailView, setShowDetailView] = useState(false)
  const [showExporter, setShowExporter] = useState(false)
  const [showFilters, setShowFilters] = useState(false)

  const {
    data: submissions,
    isLoading,
    error,
    refetch
  } = useSubmissions(formId, filters)

  const deleteSubmissionMutation = useMutation({
    mutationFn: async (submissionIds: string[]) => {
      // TODO: Implement submission deletion
      console.log('Deleting submissions:', submissionIds)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['submissions', formId] })
      setSelectedSubmissions([])
      toast.success('Submissions deleted successfully')
    },
    onError: (error) => {
      console.error('Delete error:', error)
      toast.error('Failed to delete submissions')
    }
  })

  const updateSubmissionStatusMutation = useMutation({
    mutationFn: async ({ submissionIds, status }: { submissionIds: string[], status: string }) => {
      // TODO: Implement submission status update
      console.log('Updating submission status:', submissionIds, status)
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['submissions', formId] })
      setSelectedSubmissions([])
      toast.success('Submission status updated successfully')
    },
    onError: (error) => {
      console.error('Update error:', error)
      toast.error('Failed to update submission status')
    }
  })

  // Filter submissions based on current filters
  const filteredSubmissions = useMemo(() => {
    if (!submissions) return []

    return submissions.filter(submission => {
      // Search filter
      if (filters.search) {
        const searchTerm = filters.search.toLowerCase()
        const searchableText = Object.values(submission.data)
          .join(' ')
          .toLowerCase()
        if (!searchableText.includes(searchTerm)) return false
      }

      // Status filter
      if (filters.status !== 'all' && submission.status !== filters.status) {
        return false
      }

      // Payment status filter
      if (filters.paymentStatus !== 'all' && submission.paymentStatus !== filters.paymentStatus) {
        return false
      }

      // Date range filter
      if (filters.dateRange.from || filters.dateRange.to) {
        const submissionDate = new Date(submission.metadata.submittedAt)
        if (filters.dateRange.from && submissionDate < filters.dateRange.from) return false
        if (filters.dateRange.to && submissionDate > filters.dateRange.to) return false
      }

      return true
    }).sort((a, b) => {
      const aValue = a[filters.sortBy as keyof FormSubmission] || a.metadata.submittedAt
      const bValue = b[filters.sortBy as keyof FormSubmission] || b.metadata.submittedAt
      
      if (filters.sortOrder === 'asc') {
        return aValue > bValue ? 1 : -1
      } else {
        return aValue < bValue ? 1 : -1
      }
    })
  }, [submissions, filters])

  const handleSelectSubmission = useCallback((submissionId: string) => {
    setSelectedSubmissions(prev => 
      prev.includes(submissionId)
        ? prev.filter(id => id !== submissionId)
        : [...prev, submissionId]
    )
  }, [])

  const handleSelectAll = useCallback(() => {
    if (selectedSubmissions.length === filteredSubmissions.length) {
      setSelectedSubmissions([])
    } else {
      setSelectedSubmissions(filteredSubmissions.map(s => s.id))
    }
  }, [selectedSubmissions.length, filteredSubmissions])

  const handleViewSubmission = useCallback((submission: FormSubmission) => {
    setSelectedSubmission(submission)
    setShowDetailView(true)
  }, [])

  const handleDeleteSelected = useCallback(() => {
    if (selectedSubmissions.length === 0) return
    
    if (confirm(`Are you sure you want to delete ${selectedSubmissions.length} submission(s)?`)) {
      deleteSubmissionMutation.mutate(selectedSubmissions)
    }
  }, [selectedSubmissions, deleteSubmissionMutation])

  const handleUpdateStatus = useCallback((status: string) => {
    if (selectedSubmissions.length === 0) return
    
    updateSubmissionStatusMutation.mutate({
      submissionIds: selectedSubmissions,
      status
    })
  }, [selectedSubmissions, updateSubmissionStatusMutation])

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      draft: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      abandoned: { color: 'bg-red-100 text-red-800', icon: XCircle }
    }
    
    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.completed
    const Icon = config.icon
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {status.charAt(0).toUpperCase() + status.slice(1)}
      </Badge>
    )
  }

  const getPaymentStatusBadge = (paymentStatus?: string) => {
    if (!paymentStatus) return null
    
    const statusConfig = {
      completed: { color: 'bg-green-100 text-green-800', icon: CheckCircle },
      pending: { color: 'bg-yellow-100 text-yellow-800', icon: Clock },
      failed: { color: 'bg-red-100 text-red-800', icon: XCircle },
      refunded: { color: 'bg-gray-100 text-gray-800', icon: RefreshCw }
    }
    
    const config = statusConfig[paymentStatus as keyof typeof statusConfig] || statusConfig.pending
    const Icon = config.icon
    
    return (
      <Badge className={config.color}>
        <Icon className="w-3 h-3 mr-1" />
        {paymentStatus.charAt(0).toUpperCase() + paymentStatus.slice(1)}
      </Badge>
    )
  }

  if (!hasPermission('view_submissions')) {
    return (
      <div className="text-center py-8">
        <AlertCircle className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold text-foreground mb-2">Access Denied</h3>
        <p className="text-muted-foreground">
          You don't have permission to view form submissions.
        </p>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Form Submissions</h1>
          <p className="text-muted-foreground">
            {filteredSubmissions.length} of {submissions?.length || 0} submissions
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowFilters(!showFilters)}
            className="flex items-center space-x-2"
          >
            <Filter className="w-4 h-4" />
            <span>Filters</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowExporter(true)}
            className="flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
          
          <Button
            variant="outline"
            size="sm"
            onClick={() => refetch()}
            className="flex items-center space-x-2"
          >
            <RefreshCw className="w-4 h-4" />
            <span>Refresh</span>
          </Button>
        </div>
      </div>

      {/* Filters */}
      <AnimatePresence>
        {showFilters && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.2 }}
          >
            <SubmissionFilters
              filters={filters}
              onFiltersChange={setFilters}
              schema={schema}
            />
          </motion.div>
        )}
      </AnimatePresence>

      {/* Bulk Actions */}
      {selectedSubmissions.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          className="flex items-center justify-between p-4 bg-primary/5 border border-primary/20 rounded-lg"
        >
          <span className="text-sm font-medium text-foreground">
            {selectedSubmissions.length} submission(s) selected
          </span>

          <div className="flex items-center space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUpdateStatus('completed')}
            >
              Mark Complete
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUpdateStatus('draft')}
            >
              Mark Draft
            </Button>
            <Button
              variant="destructive"
              size="sm"
              onClick={handleDeleteSelected}
            >
              Delete Selected
            </Button>
          </div>
        </motion.div>
      )}

      {/* Submissions Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead className="w-12">
                  <Checkbox
                    checked={selectedSubmissions.length === filteredSubmissions.length && filteredSubmissions.length > 0}
                    onCheckedChange={handleSelectAll}
                  />
                </TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Data Preview</TableHead>
                <TableHead>Payment</TableHead>
                <TableHead className="w-12"></TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {isLoading ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <RefreshCw className="w-6 h-6 animate-spin mx-auto mb-2" />
                    <p className="text-muted-foreground">Loading submissions...</p>
                  </TableCell>
                </TableRow>
              ) : filteredSubmissions.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2 text-muted-foreground" />
                    <p className="text-muted-foreground">No submissions found</p>
                  </TableCell>
                </TableRow>
              ) : (
                filteredSubmissions.map((submission) => (
                  <TableRow key={submission.id} className="hover:bg-muted/50">
                    <TableCell>
                      <Checkbox
                        checked={selectedSubmissions.includes(submission.id)}
                        onCheckedChange={() => handleSelectSubmission(submission.id)}
                      />
                    </TableCell>
                    <TableCell>
                      <div className="space-y-1">
                        <p className="text-sm font-medium">
                          {format(new Date(submission.metadata.submittedAt), 'MMM d, yyyy')}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {format(new Date(submission.metadata.submittedAt), 'h:mm a')}
                        </p>
                      </div>
                    </TableCell>
                    <TableCell>
                      {getStatusBadge(submission.status)}
                    </TableCell>
                    <TableCell>
                      <div className="max-w-xs truncate">
                        {Object.entries(submission.data)
                          .slice(0, 2)
                          .map(([key, value]) => (
                            <div key={key} className="text-sm">
                              <span className="font-medium">{key}:</span> {String(value)}
                            </div>
                          ))}
                      </div>
                    </TableCell>
                    <TableCell>
                      {getPaymentStatusBadge(submission.paymentStatus)}
                    </TableCell>
                    <TableCell>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" size="sm">
                            <MoreHorizontal className="w-4 h-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem onClick={() => handleViewSubmission(submission)}>
                            <Eye className="w-4 h-4 mr-2" />
                            View Details
                          </DropdownMenuItem>
                          <DropdownMenuItem onClick={() => handleDeleteSelected()}>
                            <Trash2 className="w-4 h-4 mr-2" />
                            Delete
                          </DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>

      {/* Detail View Modal */}
      <Dialog open={showDetailView} onOpenChange={setShowDetailView}>
        <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Submission Details</DialogTitle>
          </DialogHeader>
          {selectedSubmission && (
            <SubmissionDetailView
              submission={selectedSubmission}
              schema={schema}
              onClose={() => setShowDetailView(false)}
            />
          )}
        </DialogContent>
      </Dialog>

      {/* Export Modal */}
      <Dialog open={showExporter} onOpenChange={setShowExporter}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Export Submissions</DialogTitle>
          </DialogHeader>
          <SubmissionExporter
            submissions={filteredSubmissions}
            schema={schema}
            onClose={() => setShowExporter(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  )
}
