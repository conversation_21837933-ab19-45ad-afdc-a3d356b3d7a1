@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=JetBrains+Mono:wght@400;500;600&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 17.5%;
    --input: 217.2 32.6% 17.5%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

@layer components {
  .form-builder-grid {
    display: grid;
    grid-template-columns: 280px 1fr 320px;
    height: calc(100vh - 64px);
  }

  .form-field-dragging {
    @apply opacity-50 transform rotate-2;
  }

  .form-field-drop-zone {
    @apply border-2 border-dashed border-primary/50 bg-primary/5 rounded-lg p-4 min-h-[100px] flex items-center justify-center;
  }

  .form-field-drop-zone.active {
    @apply border-primary bg-primary/10;
  }

  .submission-table {
    @apply w-full border-collapse;
  }

  .submission-table th {
    @apply bg-muted/50 border-b border-border px-4 py-3 text-left text-sm font-medium text-muted-foreground;
  }

  .submission-table td {
    @apply border-b border-border px-4 py-3 text-sm;
  }

  .analytics-card {
    @apply bg-card border border-border rounded-lg p-6 shadow-sm;
  }

  .workflow-node {
    @apply bg-card border border-border rounded-lg p-4 shadow-sm min-w-[200px];
  }

  .workflow-connection {
    @apply stroke-primary stroke-2 fill-none;
  }

  .integration-card {
    @apply bg-card border border-border rounded-lg p-6 hover:shadow-md transition-shadow cursor-pointer;
  }

  .integration-card.connected {
    @apply border-green-500 bg-green-50 dark:bg-green-950;
  }

  .billing-plan-card {
    @apply bg-card border border-border rounded-lg p-6 relative;
  }

  .billing-plan-card.current {
    @apply border-primary bg-primary/5;
  }

  .billing-plan-card.popular::before {
    content: "Most Popular";
    @apply absolute -top-3 left-1/2 transform -translate-x-1/2 bg-primary text-primary-foreground px-3 py-1 rounded-full text-xs font-medium;
  }
}
