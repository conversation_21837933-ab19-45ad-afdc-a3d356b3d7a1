import { useState, useEffect, useMemo } from 'react'
import { useQuery } from '@tanstack/react-query'
import { motion } from 'framer-motion'
import { 
  BarChart, 
  Bar, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer,
  LineChart,
  Line,
  PieChart,
  Pie,
  Cell,
  Area,
  AreaChart
} from 'recharts'
import { 
  TrendingUp, 
  TrendingDown, 
  Users, 
  Eye, 
  CheckCircle, 
  Clock,
  Smartphone,
  Monitor,
  Tablet,
  Globe,
  Calendar,
  Filter,
  Download
} from 'lucide-react'
import { FormAnalytics, FormSchema } from '@/types/form'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/Card'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { Button } from '@/components/ui/Button'
import { Badge } from '@/components/ui/Badge'
import { DateRangePicker } from '@/components/ui/DateRangePicker'
import { MetricCard } from './MetricCard'
import { ConversionFunnel } from './ConversionFunnel'
import { HeatmapChart } from './HeatmapChart'
import { useFormAnalytics } from '@/hooks/useFormAnalytics'
import { useAuthStore } from '@/stores/authStore'
import { format, subDays, startOfDay, endOfDay } from 'date-fns'

interface AnalyticsDashboardProps {
  formId: string
  schema: FormSchema
}

export function AnalyticsDashboard({ formId, schema }: AnalyticsDashboardProps) {
  const { hasPermission } = useAuthStore()
  const [dateRange, setDateRange] = useState({
    from: startOfDay(subDays(new Date(), 30)),
    to: endOfDay(new Date())
  })
  const [timeframe, setTimeframe] = useState('30d')

  const {
    data: analytics,
    isLoading,
    error
  } = useFormAnalytics(formId, dateRange)

  // Calculate key metrics
  const metrics = useMemo(() => {
    if (!analytics) return null

    const conversionRate = analytics.totalViews > 0 
      ? (analytics.totalSubmissions / analytics.totalViews) * 100 
      : 0

    const avgCompletionTime = analytics.averageCompletionTime || 0
    const completionTimeMinutes = Math.round(avgCompletionTime / 60000)

    return {
      totalViews: analytics.totalViews,
      totalSubmissions: analytics.totalSubmissions,
      conversionRate: Math.round(conversionRate * 100) / 100,
      avgCompletionTime: completionTimeMinutes,
      bounceRate: 100 - analytics.completionRate
    }
  }, [analytics])

  // Prepare chart data
  const chartData = useMemo(() => {
    if (!analytics) return { daily: [], devices: [], sources: [] }

    // Mock daily data - in real app, this would come from analytics
    const daily = Array.from({ length: 30 }, (_, i) => {
      const date = subDays(new Date(), 29 - i)
      return {
        date: format(date, 'MMM dd'),
        views: Math.floor(Math.random() * 100) + 20,
        submissions: Math.floor(Math.random() * 30) + 5,
        conversionRate: Math.random() * 15 + 5
      }
    })

    const devices = [
      { name: 'Desktop', value: analytics.deviceBreakdown.desktop, color: '#8884d8' },
      { name: 'Mobile', value: analytics.deviceBreakdown.mobile, color: '#82ca9d' },
      { name: 'Tablet', value: analytics.deviceBreakdown.tablet, color: '#ffc658' }
    ]

    const sources = Object.entries(analytics.sourceBreakdown).map(([source, count]) => ({
      name: source,
      value: count,
      percentage: Math.round((count / analytics.totalViews) * 100)
    }))

    return { daily, devices, sources }
  }, [analytics])

  const handleTimeframeChange = (value: string) => {
    setTimeframe(value)
    const days = parseInt(value.replace('d', ''))
    setDateRange({
      from: startOfDay(subDays(new Date(), days)),
      to: endOfDay(new Date())
    })
  }

  const handleExport = () => {
    // TODO: Implement analytics export
    console.log('Exporting analytics data...')
  }

  if (!hasPermission('view_analytics')) {
    return (
      <div className="text-center py-8">
        <BarChart className="w-12 h-12 mx-auto text-muted-foreground mb-4" />
        <h3 className="text-lg font-semibold text-foreground mb-2">Access Denied</h3>
        <p className="text-muted-foreground">
          You don't have permission to view form analytics.
        </p>
      </div>
    )
  }

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-8 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-64 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-foreground">Form Analytics</h1>
          <p className="text-muted-foreground">
            Insights and performance metrics for {schema.title}
          </p>
        </div>
        
        <div className="flex items-center space-x-3">
          <Select value={timeframe} onValueChange={handleTimeframeChange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
            </SelectContent>
          </Select>
          
          <DateRangePicker
            value={dateRange}
            onChange={setDateRange}
          />
          
          <Button
            variant="outline"
            size="sm"
            onClick={handleExport}
            className="flex items-center space-x-2"
          >
            <Download className="w-4 h-4" />
            <span>Export</span>
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-6">
          <MetricCard
            title="Total Views"
            value={metrics.totalViews.toLocaleString()}
            icon={Eye}
            trend={12.5}
            trendLabel="vs last period"
          />
          <MetricCard
            title="Submissions"
            value={metrics.totalSubmissions.toLocaleString()}
            icon={CheckCircle}
            trend={8.2}
            trendLabel="vs last period"
          />
          <MetricCard
            title="Conversion Rate"
            value={`${metrics.conversionRate}%`}
            icon={TrendingUp}
            trend={-2.1}
            trendLabel="vs last period"
          />
          <MetricCard
            title="Avg. Completion Time"
            value={`${metrics.avgCompletionTime}m`}
            icon={Clock}
            trend={-15.3}
            trendLabel="vs last period"
          />
          <MetricCard
            title="Bounce Rate"
            value={`${metrics.bounceRate.toFixed(1)}%`}
            icon={TrendingDown}
            trend={5.7}
            trendLabel="vs last period"
            invertTrend
          />
        </div>
      )}

      {/* Charts Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Daily Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <BarChart className="w-5 h-5" />
              <span>Daily Performance</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <AreaChart data={chartData.daily}>
                <CartesianGrid strokeDasharray="3 3" />
                <XAxis dataKey="date" />
                <YAxis />
                <Tooltip />
                <Area 
                  type="monotone" 
                  dataKey="views" 
                  stackId="1"
                  stroke="#8884d8" 
                  fill="#8884d8" 
                  fillOpacity={0.6}
                />
                <Area 
                  type="monotone" 
                  dataKey="submissions" 
                  stackId="2"
                  stroke="#82ca9d" 
                  fill="#82ca9d" 
                  fillOpacity={0.6}
                />
              </AreaChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Device Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Monitor className="w-5 h-5" />
              <span>Device Breakdown</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <ResponsiveContainer width="100%" height={300}>
              <PieChart>
                <Pie
                  data={chartData.devices}
                  cx="50%"
                  cy="50%"
                  labelLine={false}
                  label={({ name, percent }) => `${name} ${(percent * 100).toFixed(0)}%`}
                  outerRadius={80}
                  fill="#8884d8"
                  dataKey="value"
                >
                  {chartData.devices.map((entry, index) => (
                    <Cell key={`cell-${index}`} fill={entry.color} />
                  ))}
                </Pie>
                <Tooltip />
              </PieChart>
            </ResponsiveContainer>
          </CardContent>
        </Card>

        {/* Conversion Funnel */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <TrendingDown className="w-5 h-5" />
              <span>Conversion Funnel</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {analytics && (
              <ConversionFunnel
                data={analytics.conversionFunnel}
                height={300}
              />
            )}
          </CardContent>
        </Card>

        {/* Traffic Sources */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Globe className="w-5 h-5" />
              <span>Traffic Sources</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {chartData.sources.map((source, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex items-center space-x-3">
                    <div className="w-3 h-3 rounded-full bg-primary"></div>
                    <span className="text-sm font-medium">{source.name}</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <span className="text-sm text-muted-foreground">
                      {source.value} visits
                    </span>
                    <Badge variant="secondary">
                      {source.percentage}%
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Abandonment Analysis */}
      {analytics && analytics.abandonmentPoints.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center space-x-2">
              <Users className="w-5 h-5" />
              <span>Form Abandonment Analysis</span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <HeatmapChart
              data={analytics.abandonmentPoints}
              schema={schema}
              height={400}
            />
          </CardContent>
        </Card>
      )}
    </div>
  )
}
