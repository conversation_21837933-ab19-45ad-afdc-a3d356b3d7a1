import { forwardRef, useCallback } from 'react'
import { useDrop } from 'react-dnd'
import { motion, AnimatePresence } from 'framer-motion'
import { Plus } from 'lucide-react'
import { FormSchema, FormField, FieldType, DragItem } from '@/types/form'
import { DraggableFormField } from './DraggableFormField'
import { Button } from '@/components/ui/Button'

interface FormCanvasProps {
  schema: FormSchema
  selectedFieldId?: string
  onSelectField: (fieldId: string) => void
  onUpdateField: (fieldId: string, updates: Partial<FormField>) => void
  onDeleteField: (fieldId: string) => void
  onMoveField: (dragIndex: number, hoverIndex: number) => void
  onAddField: (fieldType: FieldType, position?: { x: number; y: number }) => void
}

export const FormCanvas = forwardRef<HTMLDivElement, FormCanvasProps>(({
  schema,
  selectedFieldId,
  onSelectField,
  onUpdateField,
  onDeleteField,
  onMoveField,
  onAddField
}, ref) => {
  const [{ isOver, canDrop }, drop] = useDrop(() => ({
    accept: 'field',
    drop: (item: DragItem, monitor) => {
      if (!monitor.didDrop()) {
        const offset = monitor.getClientOffset()
        const canvasRect = (ref as any)?.current?.getBoundingClientRect()

        if (offset && canvasRect) {
          const position = {
            x: offset.x - canvasRect.left,
            y: offset.y - canvasRect.top
          }

          if (item.id) {
            // Moving existing field
            const fieldIndex = schema.fields.findIndex(f => f.id === item.id)
            if (fieldIndex !== -1) {
              onUpdateField(item.id, { position })
            }
          } else {
            // Adding new field
            onAddField(item.fieldType, position)
          }
        }
      }
    },
    collect: (monitor) => ({
      isOver: monitor.isOver({ shallow: true }),
      canDrop: monitor.canDrop()
    })
  }))

  const handleCanvasClick = useCallback((e: React.MouseEvent) => {
    // Deselect field when clicking on empty canvas
    if (e.target === e.currentTarget) {
      onSelectField('')
    }
  }, [onSelectField])

  const isEmpty = schema.fields.length === 0

  return (
    <div
      ref={(node) => {
        drop(node)
        if (ref) {
          if (typeof ref === 'function') {
            ref(node)
          } else {
            ref.current = node
          }
        }
      }}
      className={`
        relative w-full h-full overflow-auto bg-muted/30 p-8
        ${isOver && canDrop ? 'bg-primary/5' : ''}
        transition-colors duration-200
      `}
      onClick={handleCanvasClick}
    >
      {/* Form Header */}
      <div className="max-w-2xl mx-auto mb-8">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h1 className="text-2xl font-bold text-foreground mb-2">
            {schema.title || 'Untitled Form'}
          </h1>
          {schema.description && (
            <p className="text-muted-foreground">
              {schema.description}
            </p>
          )}
        </div>
      </div>

      {/* Form Fields */}
      <div className="max-w-2xl mx-auto space-y-4">
        <AnimatePresence>
          {schema.fields.map((field, index) => (
            <motion.div
              key={field.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              transition={{ duration: 0.2 }}
            >
              <DraggableFormField
                field={field}
                index={index}
                isSelected={selectedFieldId === field.id}
                onSelect={() => onSelectField(field.id)}
                onUpdate={(updates) => onUpdateField(field.id, updates)}
                onDelete={() => onDeleteField(field.id)}
                onMove={onMoveField}
              />
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Empty State */}
        {isEmpty && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            className="text-center py-16"
          >
            <div className="max-w-md mx-auto">
              <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-primary/10 flex items-center justify-center">
                <Plus className="w-8 h-8 text-primary" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                Start Building Your Form
              </h3>
              <p className="text-muted-foreground mb-6">
                Drag fields from the sidebar or click the buttons below to add your first field.
              </p>
              <div className="flex flex-wrap gap-2 justify-center">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddField('text')}
                >
                  Add Text Field
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddField('email')}
                >
                  Add Email Field
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onAddField('textarea')}
                >
                  Add Text Area
                </Button>
              </div>
            </div>
          </motion.div>
        )}

        {/* Drop Zone Indicator */}
        {isOver && canDrop && (
          <motion.div
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            className="form-field-drop-zone active"
          >
            <Plus className="w-6 h-6 text-primary" />
            <span className="text-sm font-medium text-primary">
              Drop field here
            </span>
          </motion.div>
        )}
      </div>

      {/* Form Footer */}
      <div className="max-w-2xl mx-auto mt-8">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <div className="flex justify-center">
            <Button size="lg" className="px-8">
              Submit Form
            </Button>
          </div>
        </div>
      </div>
    </div>
  )
})
