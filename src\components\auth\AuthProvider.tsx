import { useEffect, ReactNode } from 'react'
import { useAuthStore } from '@/stores/authStore'
import { supabase } from '@/lib/supabase'
import { LoadingSpinner } from '@/components/ui/LoadingSpinner'

interface AuthProviderProps {
  children: ReactNode
}

export function AuthProvider({ children }: AuthProviderProps) {
  const { initialize, setUser, setSession, isLoading, isInitialized } = useAuthStore()

  useEffect(() => {
    // Initialize auth state
    initialize()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.email)

        setSession(session)
        setUser(session?.user ?? null)

        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          // Re-initialize to fetch user data
          await initialize()
        } else if (event === 'SIGNED_OUT') {
          // Clear all auth state
          useAuthStore.getState().signOut()
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [initialize, setUser, setSession])

  if (!isInitialized || isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center">
          <LoadingSpinner size="lg" />
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
