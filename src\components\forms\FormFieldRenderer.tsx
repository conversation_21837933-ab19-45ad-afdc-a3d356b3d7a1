import { useState, useCallback } from 'react'
import { motion } from 'framer-motion'
import { FormField } from '@/types/form'
import { Input } from '@/components/ui/Input'
import { Textarea } from '@/components/ui/Textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/Select'
import { RadioGroup, RadioGroupItem } from '@/components/ui/RadioGroup'
import { Checkbox } from '@/components/ui/Checkbox'
import { Label } from '@/components/ui/Label'
import { Button } from '@/components/ui/Button'
import { FileUpload } from '@/components/ui/FileUpload'
import { SignatureCanvas } from '@/components/ui/SignatureCanvas'
import { RatingField } from '@/components/ui/RatingField'
import { SliderField } from '@/components/ui/SliderField'
import { PaymentField } from '@/components/ui/PaymentField'
import { DatePicker } from '@/components/ui/DatePicker'
import { TimePicker } from '@/components/ui/TimePicker'
import { Alert, AlertDescription } from '@/components/ui/Alert'
import { AlertCircle, Upload, Star, CreditCard } from 'lucide-react'

interface FormFieldRendererProps {
  field: FormField
  value: any
  onChange: (value: any) => void
  onBlur: () => void
  error?: string
  onInteraction: (action: string) => void
}

export function FormFieldRenderer({
  field,
  value,
  onChange,
  onBlur,
  error,
  onInteraction
}: FormFieldRendererProps) {
  const [isFocused, setIsFocused] = useState(false)

  const handleFocus = useCallback(() => {
    setIsFocused(true)
    onInteraction('focus')
  }, [onInteraction])

  const handleBlur = useCallback(() => {
    setIsFocused(false)
    onBlur()
    onInteraction('blur')
  }, [onBlur, onInteraction])

  const handleChange = useCallback((newValue: any) => {
    onChange(newValue)
    onInteraction('change')
  }, [onChange, onInteraction])

  const renderField = () => {
    switch (field.type) {
      case 'text':
      case 'email':
      case 'phone':
      case 'url':
      case 'password':
        return (
          <Input
            type={field.type === 'phone' ? 'tel' : field.type}
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'number':
        return (
          <Input
            type="number"
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => handleChange(parseFloat(e.target.value) || '')}
            onFocus={handleFocus}
            onBlur={handleBlur}
            min={field.validation?.min}
            max={field.validation?.max}
            step={field.settings?.step}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'textarea':
        return (
          <Textarea
            placeholder={field.placeholder}
            value={value || ''}
            onChange={(e) => handleChange(e.target.value)}
            onFocus={handleFocus}
            onBlur={handleBlur}
            rows={field.settings?.rows || 4}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'select':
        return (
          <Select value={value || ''} onValueChange={handleChange}>
            <SelectTrigger className={error ? 'border-destructive' : ''}>
              <SelectValue placeholder={field.placeholder || 'Select an option'} />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map((option) => (
                <SelectItem key={option.id} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'radio':
        return (
          <RadioGroup value={value || ''} onValueChange={handleChange}>
            <div className="space-y-3">
              {field.options?.map((option) => (
                <div key={option.id} className="flex items-center space-x-2">
                  <RadioGroupItem value={option.value} id={option.id} />
                  <Label htmlFor={option.id} className="text-sm font-normal">
                    {option.label}
                  </Label>
                </div>
              ))}
            </div>
          </RadioGroup>
        )

      case 'checkbox':
        return (
          <div className="space-y-3">
            {field.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={Array.isArray(value) ? value.includes(option.value) : false}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(value) ? value : []
                    if (checked) {
                      handleChange([...currentValues, option.value])
                    } else {
                      handleChange(currentValues.filter((v: any) => v !== option.value))
                    }
                  }}
                />
                <Label htmlFor={option.id} className="text-sm font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        )

      case 'multi_select':
        return (
          <div className="space-y-2">
            {field.options?.map((option) => (
              <div key={option.id} className="flex items-center space-x-2">
                <Checkbox
                  id={option.id}
                  checked={Array.isArray(value) ? value.includes(option.value) : false}
                  onCheckedChange={(checked) => {
                    const currentValues = Array.isArray(value) ? value : []
                    if (checked) {
                      handleChange([...currentValues, option.value])
                    } else {
                      handleChange(currentValues.filter((v: any) => v !== option.value))
                    }
                  }}
                />
                <Label htmlFor={option.id} className="text-sm font-normal">
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        )

      case 'date':
        return (
          <DatePicker
            value={value}
            onChange={handleChange}
            placeholder={field.placeholder}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'time':
        return (
          <TimePicker
            value={value}
            onChange={handleChange}
            placeholder={field.placeholder}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'datetime':
        return (
          <div className="space-y-2">
            <DatePicker
              value={value?.date}
              onChange={(date) => handleChange({ ...value, date })}
              placeholder="Select date"
              className={error ? 'border-destructive' : ''}
            />
            <TimePicker
              value={value?.time}
              onChange={(time) => handleChange({ ...value, time })}
              placeholder="Select time"
              className={error ? 'border-destructive' : ''}
            />
          </div>
        )

      case 'file':
      case 'image':
        return (
          <FileUpload
            accept={field.settings?.accept || (field.type === 'image' ? 'image/*' : '*')}
            multiple={field.settings?.multiple}
            maxSize={field.settings?.maxSize}
            value={value}
            onChange={handleChange}
            onError={(error) => onInteraction(`error:${error}`)}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'signature':
        return (
          <SignatureCanvas
            value={value}
            onChange={handleChange}
            width={field.size?.width || 400}
            height={field.settings?.height || 150}
            className={error ? 'border-destructive' : ''}
          />
        )

      case 'rating':
        return (
          <RatingField
            value={value || 0}
            onChange={handleChange}
            maxRating={field.settings?.maxRating || 5}
            icon={field.settings?.icon || 'star'}
            size="lg"
          />
        )

      case 'slider':
        return (
          <SliderField
            value={value || 0}
            onChange={handleChange}
            min={field.validation?.min || 0}
            max={field.validation?.max || 100}
            step={field.settings?.step || 1}
            minLabel={field.settings?.minLabel}
            maxLabel={field.settings?.maxLabel}
          />
        )

      case 'payment':
        return (
          <PaymentField
            amount={field.settings?.amount}
            currency={field.settings?.currency || 'USD'}
            allowCustomAmount={field.settings?.allowCustomAmount}
            value={value}
            onChange={handleChange}
            onError={(error) => onInteraction(`error:${error}`)}
          />
        )

      case 'divider':
        return <hr className="border-border my-6" />

      case 'heading':
        const HeadingTag = field.settings?.size === 'large' ? 'h1' :
                          field.settings?.size === 'medium' ? 'h2' : 'h3'
        return (
          <HeadingTag
            className={`font-bold text-foreground ${
              field.settings?.size === 'large' ? 'text-3xl' :
              field.settings?.size === 'medium' ? 'text-2xl' : 'text-xl'
            } ${field.settings?.alignment === 'center' ? 'text-center' :
                field.settings?.alignment === 'right' ? 'text-right' : 'text-left'}`}
          >
            {field.label}
          </HeadingTag>
        )

      case 'paragraph':
        return (
          <p className={`text-muted-foreground ${
            field.settings?.alignment === 'center' ? 'text-center' :
            field.settings?.alignment === 'right' ? 'text-right' : 'text-left'
          }`}>
            {field.description || field.label}
          </p>
        )

      case 'html':
        return (
          <div
            className="prose prose-sm max-w-none"
            dangerouslySetInnerHTML={{ __html: field.description || field.label }}
          />
        )

      default:
        return (
          <div className="p-4 border border-dashed border-border rounded-lg text-center text-muted-foreground">
            Unsupported field type: {field.type}
          </div>
        )
    }
  }

  // Don't render container for layout fields
  if (['divider', 'heading', 'paragraph', 'html'].includes(field.type)) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.2 }}
      >
        {renderField()}
      </motion.div>
    )
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 10 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.2 }}
      className={`space-y-2 ${isFocused ? 'ring-2 ring-primary/20 rounded-lg p-1 -m-1' : ''}`}
    >
      {/* Field Label */}
      <div className="flex items-center justify-between">
        <Label htmlFor={field.id} className="text-sm font-medium text-foreground">
          {field.label}
          {field.required && <span className="text-destructive ml-1">*</span>}
        </Label>
      </div>

      {/* Field Description */}
      {field.description && (
        <p className="text-sm text-muted-foreground">
          {field.description}
        </p>
      )}

      {/* Field Input */}
      <div className="relative">
        {renderField()}
      </div>

      {/* Field Error */}
      {error && (
        <Alert variant="destructive" className="mt-2">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription className="text-sm">
            {error}
          </AlertDescription>
        </Alert>
      )}
    </motion.div>
  )
}
