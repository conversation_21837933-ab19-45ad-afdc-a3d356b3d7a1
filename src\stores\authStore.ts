import { create } from 'zustand'
import { persist } from 'zustand/middleware'
import { User, Session } from '@supabase/supabase-js'
import { supabase } from '@/lib/supabase'
import { Database } from '@/types/supabase'

type UserProfile = Database['public']['Tables']['user_profiles']['Row']
type Tenant = Database['public']['Tables']['tenants']['Row']
type TeamMember = Database['public']['Tables']['team_members']['Row'] & {
  tenant: Tenant
}

interface AuthState {
  user: User | null
  session: Session | null
  profile: UserProfile | null
  currentTenant: Tenant | null
  tenants: TeamMember[]
  isLoading: boolean
  isInitialized: boolean
}

interface AuthActions {
  setUser: (user: User | null) => void
  setSession: (session: Session | null) => void
  setProfile: (profile: UserProfile | null) => void
  setCurrentTenant: (tenant: Tenant | null) => void
  setTenants: (tenants: TeamMember[]) => void
  setLoading: (loading: boolean) => void
  setInitialized: (initialized: boolean) => void
  initialize: () => Promise<void>
  signOut: () => Promise<void>
  switchTenant: (tenantId: string) => void
  createTenant: (name: string, slug: string) => Promise<Tenant | null>
  inviteTeamMember: (email: string, role: 'admin' | 'editor' | 'viewer') => Promise<boolean>
  updateTeamMemberRole: (memberId: string, role: 'admin' | 'editor' | 'viewer') => Promise<boolean>
  removeTeamMember: (memberId: string) => Promise<boolean>
  getCurrentUserRole: () => 'owner' | 'admin' | 'editor' | 'viewer' | null
  hasPermission: (permission: string) => boolean
}

export const useAuthStore = create<AuthState & AuthActions>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      session: null,
      profile: null,
      currentTenant: null,
      tenants: [],
      isLoading: true,
      isInitialized: false,

      // Actions
      setUser: (user) => set({ user }),
      setSession: (session) => set({ session }),
      setProfile: (profile) => set({ profile }),
      setCurrentTenant: (tenant) => set({ currentTenant: tenant }),
      setTenants: (tenants) => set({ tenants }),
      setLoading: (loading) => set({ isLoading: loading }),
      setInitialized: (initialized) => set({ isInitialized: initialized }),

      initialize: async () => {
        try {
          set({ isLoading: true })

          // Get current session
          const { data: { session }, error: sessionError } = await supabase.auth.getSession()
          if (sessionError) throw sessionError

          if (session?.user) {
            set({ session, user: session.user })

            // Get user profile
            const { data: profile, error: profileError } = await supabase
              .from('user_profiles')
              .select('*')
              .eq('id', session.user.id)
              .single()

            if (profileError && profileError.code !== 'PGRST116') {
              console.error('Error fetching profile:', profileError)
            } else if (profile) {
              set({ profile })
            }

            // Get user tenants
            const { data: tenants, error: tenantsError } = await supabase
              .from('team_members')
              .select(`
                *,
                tenant:tenants(*)
              `)
              .eq('user_id', session.user.id)

            if (tenantsError) {
              console.error('Error fetching tenants:', tenantsError)
            } else if (tenants) {
              set({ tenants: tenants as TeamMember[] })

              // Set current tenant (first one or previously selected)
              const currentTenantId = localStorage.getItem('currentTenantId')
              const currentTenant = tenants.find(t => t.tenant.id === currentTenantId)?.tenant || tenants[0]?.tenant
              if (currentTenant) {
                set({ currentTenant })
              }
            }
          }
        } catch (error) {
          console.error('Error initializing auth:', error)
        } finally {
          set({ isLoading: false, isInitialized: true })
        }
      },

      signOut: async () => {
        try {
          const { error } = await supabase.auth.signOut()
          if (error) throw error

          set({
            user: null,
            session: null,
            profile: null,
            currentTenant: null,
            tenants: []
          })

          localStorage.removeItem('currentTenantId')
        } catch (error) {
          console.error('Error signing out:', error)
          throw error
        }
      },

      switchTenant: (tenantId) => {
        const { tenants } = get()
        const tenant = tenants.find(t => t.tenant.id === tenantId)?.tenant
        if (tenant) {
          set({ currentTenant: tenant })
          localStorage.setItem('currentTenantId', tenantId)
        }
      },

      createTenant: async (name, slug) => {
        try {
          const { user } = get()
          if (!user) throw new Error('User not authenticated')

          // Create tenant
          const { data: tenant, error: tenantError } = await supabase
            .from('tenants')
            .insert({ name, slug })
            .select()
            .single()

          if (tenantError) throw tenantError

          // Add user as owner
          const { error: memberError } = await supabase
            .from('team_members')
            .insert({
              tenant_id: tenant.id,
              user_id: user.id,
              role: 'owner'
            })

          if (memberError) throw memberError

          // Refresh tenants
          await get().initialize()

          return tenant
        } catch (error) {
          console.error('Error creating tenant:', error)
          return null
        }
      },

      inviteTeamMember: async (email, role) => {
        try {
          const { currentTenant } = get()
          if (!currentTenant) throw new Error('No current tenant')

          // TODO: Implement team member invitation logic
          // This would typically involve sending an email invitation

          return true
        } catch (error) {
          console.error('Error inviting team member:', error)
          return false
        }
      },

      updateTeamMemberRole: async (memberId, role) => {
        try {
          const { error } = await supabase
            .from('team_members')
            .update({ role })
            .eq('id', memberId)

          if (error) throw error

          // Refresh tenants
          await get().initialize()

          return true
        } catch (error) {
          console.error('Error updating team member role:', error)
          return false
        }
      },

      removeTeamMember: async (memberId) => {
        try {
          const { error } = await supabase
            .from('team_members')
            .delete()
            .eq('id', memberId)

          if (error) throw error

          // Refresh tenants
          await get().initialize()

          return true
        } catch (error) {
          console.error('Error removing team member:', error)
          return false
        }
      },

      getCurrentUserRole: () => {
        const { currentTenant, tenants } = get()
        if (!currentTenant) return null

        const membership = tenants.find(t => t.tenant.id === currentTenant.id)
        return membership?.role || null
      },

      hasPermission: (permission) => {
        const role = get().getCurrentUserRole()
        if (!role) return false

        const permissions = {
          owner: ['all'],
          admin: ['manage_forms', 'manage_team', 'view_analytics', 'manage_integrations'],
          editor: ['manage_forms', 'view_analytics'],
          viewer: ['view_forms', 'view_analytics']
        }

        return permissions[role]?.includes(permission) || permissions[role]?.includes('all') || false
      }
    }),
    {
      name: 'auth-store',
      partialize: (state) => ({
        currentTenant: state.currentTenant
      })
    }
  )
)
