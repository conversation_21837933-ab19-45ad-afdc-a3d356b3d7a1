import { useState, useEffect, useCallback } from 'react'
import { use<PERSON><PERSON>, Controller } from 'react-hook-form'
import { zodR<PERSON>olver } from '@hookform/resolvers/zod'
import { motion, AnimatePresence } from 'framer-motion'
import { Loader2, CheckCircle, AlertCircle } from 'lucide-react'
import { FormSchema, FormField, FormSubmission } from '@/types/form'
import { FormFieldRenderer } from './FormFieldRenderer'
import { ConditionalLogicEngine } from '@/lib/conditionalLogic'
import { FormValidationSchema } from '@/lib/formValidation'
import { Button } from '@/components/ui/Button'
import { Progress } from '@/components/ui/Progress'
import { Alert, AlertDescription } from '@/components/ui/Alert'
import { useFormSubmission } from '@/hooks/useFormSubmission'
import { useFormAnalytics } from '@/hooks/useFormAnalytics'
import { toast } from 'react-hot-toast'

interface FormRendererProps {
  schema: FormSchema
  onSubmit?: (data: Record<string, any>) => void
  onProgress?: (progress: number) => void
  className?: string
  showProgress?: boolean
  allowSaveProgress?: boolean
  customTheme?: Record<string, any>
}

export function FormRenderer({
  schema,
  onSubmit,
  onProgress,
  className = '',
  showProgress = true,
  allowSaveProgress = false,
  customTheme
}: FormRendererProps) {
  const [currentStep, setCurrentStep] = useState(0)
  const [visibleFields, setVisibleFields] = useState<string[]>([])
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [isSubmitted, setIsSubmitted] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [startTime] = useState(Date.now())

  const conditionalEngine = new ConditionalLogicEngine(schema.fields)
  const validationSchema = FormValidationSchema.create(schema.fields)

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    getValues,
    formState: { errors, isValid }
  } = useForm({
    resolver: zodResolver(validationSchema),
    mode: 'onChange'
  })

  const { submitForm, saveProgress } = useFormSubmission(schema.id)
  const { trackFormView, trackFieldInteraction, trackFormSubmission } = useFormAnalytics(schema.id)

  const watchedValues = watch()

  // Track form view on mount
  useEffect(() => {
    trackFormView()
  }, [trackFormView])

  // Update visible fields based on conditional logic
  useEffect(() => {
    const visible = conditionalEngine.evaluateVisibility(watchedValues)
    setVisibleFields(visible)
  }, [watchedValues, conditionalEngine])

  // Calculate progress
  const progress = useCallback(() => {
    const totalFields = schema.fields.filter(field =>
      visibleFields.includes(field.id) &&
      !['divider', 'heading', 'paragraph', 'html'].includes(field.type)
    ).length

    const completedFields = Object.keys(watchedValues).filter(key => {
      const value = watchedValues[key]
      return value !== undefined && value !== '' && value !== null
    }).length

    const progressPercent = totalFields > 0 ? (completedFields / totalFields) * 100 : 0
    onProgress?.(progressPercent)
    return progressPercent
  }, [watchedValues, visibleFields, schema.fields, onProgress])

  // Handle form submission
  const onFormSubmit = async (data: Record<string, any>) => {
    setIsSubmitting(true)
    setSubmitError(null)

    try {
      const completionTime = Date.now() - startTime

      const submissionData: Partial<FormSubmission> = {
        formId: schema.id,
        data,
        metadata: {
          submittedAt: new Date().toISOString(),
          completionTime,
          userAgent: navigator.userAgent,
          referrer: document.referrer
        },
        status: 'completed'
      }

      await submitForm(submissionData)
      await trackFormSubmission(completionTime)

      setIsSubmitted(true)
      onSubmit?.(data)

      toast.success('Form submitted successfully!')
    } catch (error) {
      console.error('Form submission error:', error)
      setSubmitError(error instanceof Error ? error.message : 'Failed to submit form')
      toast.error('Failed to submit form. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  // Handle save progress
  const handleSaveProgress = async () => {
    try {
      const data = getValues()
      await saveProgress(data)
      toast.success('Progress saved!')
    } catch (error) {
      console.error('Save progress error:', error)
      toast.error('Failed to save progress')
    }
  }

  // Handle field interaction tracking
  const handleFieldInteraction = useCallback((fieldId: string, action: string) => {
    trackFieldInteraction(fieldId, action)
  }, [trackFieldInteraction])

  // Apply custom theme
  const themeStyles = customTheme ? {
    '--primary': customTheme.primaryColor,
    '--background': customTheme.backgroundColor,
    '--foreground': customTheme.textColor,
    '--font-family': customTheme.fontFamily,
    '--border-radius': `${customTheme.borderRadius}px`
  } as React.CSSProperties : {}

  if (isSubmitted) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        className="max-w-2xl mx-auto p-8 text-center"
        style={themeStyles}
      >
        <div className="bg-card border border-border rounded-lg p-8 shadow-sm">
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            transition={{ delay: 0.2, type: 'spring' }}
            className="w-16 h-16 mx-auto mb-4 rounded-full bg-green-100 flex items-center justify-center"
          >
            <CheckCircle className="w-8 h-8 text-green-600" />
          </motion.div>

          <h2 className="text-2xl font-bold text-foreground mb-4">
            Thank You!
          </h2>

          <p className="text-muted-foreground mb-6">
            {schema.settings.notifications.confirmationEmailTemplate ||
             'Your form has been submitted successfully. We\'ll get back to you soon.'}
          </p>

          {schema.settings.behavior.showProgressBar && (
            <div className="mb-4">
              <Progress value={100} className="w-full" />
              <p className="text-sm text-muted-foreground mt-2">
                Form completed in {Math.round((Date.now() - startTime) / 1000)} seconds
              </p>
            </div>
          )}
        </div>
      </motion.div>
    )
  }

  return (
    <div
      className={`max-w-2xl mx-auto p-6 ${className}`}
      style={themeStyles}
    >
      {/* Form Header */}
      <div className="mb-8">
        <div className="bg-card border border-border rounded-lg p-6 shadow-sm">
          <h1 className="text-3xl font-bold text-foreground mb-4">
            {schema.title}
          </h1>
          {schema.description && (
            <p className="text-muted-foreground text-lg">
              {schema.description}
            </p>
          )}
        </div>
      </div>

      {/* Progress Bar */}
      {showProgress && schema.settings.behavior.showProgressBar && (
        <div className="mb-6">
          <Progress value={progress()} className="w-full" />
          <p className="text-sm text-muted-foreground mt-2 text-center">
            {Math.round(progress())}% completed
          </p>
        </div>
      )}

      {/* Error Alert */}
      {submitError && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>{submitError}</AlertDescription>
        </Alert>
      )}

      {/* Form Fields */}
      <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
        <AnimatePresence>
          {schema.fields
            .filter(field => visibleFields.includes(field.id))
            .map((field, index) => (
              <motion.div
                key={field.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.3, delay: index * 0.1 }}
              >
                <Controller
                  name={field.id}
                  control={control}
                  defaultValue={field.defaultValue}
                  render={({ field: controllerField, fieldState }) => (
                    <FormFieldRenderer
                      field={field}
                      value={controllerField.value}
                      onChange={controllerField.onChange}
                      onBlur={controllerField.onBlur}
                      error={fieldState.error?.message}
                      onInteraction={(action) => handleFieldInteraction(field.id, action)}
                    />
                  )}
                />
              </motion.div>
            ))}
        </AnimatePresence>

        {/* Form Actions */}
        <div className="flex flex-col sm:flex-row gap-4 pt-6">
          {allowSaveProgress && schema.settings.behavior.saveProgress && (
            <Button
              type="button"
              variant="outline"
              onClick={handleSaveProgress}
              className="flex-1"
            >
              Save Progress
            </Button>
          )}

          <Button
            type="submit"
            disabled={isSubmitting || !isValid}
            className="flex-1"
          >
            {isSubmitting ? (
              <>
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                Submitting...
              </>
            ) : (
              'Submit Form'
            )}
          </Button>
        </div>
      </form>
    </div>
  )
}
